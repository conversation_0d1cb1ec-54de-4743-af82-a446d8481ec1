package com.zsm.config;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 实体类后处理工具
 * 用于修改代码生成器生成的实体类，将老版本Swagger注解替换为OpenAPI 3.0注解
 *
 * <AUTHOR>
 * @since 2024
 */
public class EntityPostProcessor {

    /**
     * 处理生成的实体类文件
     * 替换老版本Swagger注解为OpenAPI 3.0注解
     *
     * @param entityFilePath 实体类文件路径
     */
    public static void processEntityFile(String entityFilePath) {
        try {
            Path path = Paths.get(entityFilePath);
            if (!Files.exists(path)) {
                System.out.println("文件不存在: " + entityFilePath);
                return;
            }

            List<String> lines = Files.readAllLines(path);
            List<String> processedLines = lines.stream()
                    .map(EntityPostProcessor::processLine)
                    .collect(Collectors.toList());

            Files.write(path, processedLines);
            System.out.println("已处理实体类文件: " + entityFilePath);

        } catch (IOException e) {
            System.err.println("处理文件失败: " + entityFilePath + ", 错误: " + e.getMessage());
        }
    }

    /**
     * 处理单行代码
     *
     * @param line 原始行
     * @return 处理后的行
     */
    private static String processLine(String line) {
        // 替换导入语句
        if (line.contains("import io.swagger.annotations.ApiModel;")) {
            return "";
        }
        if (line.contains("import io.swagger.annotations.ApiModelProperty;")) {
            return "import io.swagger.v3.oas.annotations.media.Schema;";
        }

        // 替换注解
        if (line.contains("@ApiModel(")) {
            // 提取description
            String description = extractDescription(line, "description");
            if (description != null) {
                return line.replaceFirst("@ApiModel.*", "@Schema(description = \"" + description + "\")");
            }
            return "@Schema";
        }

        if (line.contains("@ApiModelProperty(")) {
            // 提取description
            String description = extractDescription(line, null);
            if (description != null) {
                return line.replaceFirst("@ApiModelProperty.*", "@Schema(description = \"" + description + "\")");
            }
            return "@Schema";
        }

        return line;
    }

    /**
     * 从注解中提取description值
     *
     * @param line 包含注解的行
     * @param key  要提取的key，如果为null则提取引号内的值
     * @return description值
     */
    private static String extractDescription(String line, String key) {
        try {
            if (key != null) {
                // 查找 key = "value" 模式
                String pattern = key + "\\s*=\\s*\"([^\"]+)\"";
                java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern);
                java.util.regex.Matcher m = p.matcher(line);
                if (m.find()) {
                    return m.group(1);
                }
            } else {
                // 查找第一个引号内的值
                java.util.regex.Pattern p = java.util.regex.Pattern.compile("\"([^\"]+)\"");
                java.util.regex.Matcher m = p.matcher(line);
                if (m.find()) {
                    return m.group(1);
                }
            }
        } catch (Exception e) {
            // 忽略异常，返回null
        }
        return null;
    }

    /**
     * 批量处理实体类目录
     *
     * @param entityDirPath 实体类目录路径
     */
    public static void processEntityDirectory(String entityDirPath) {
        try {
            Path dir = Paths.get(entityDirPath);
            if (!Files.exists(dir) || !Files.isDirectory(dir)) {
                System.out.println("目录不存在: " + entityDirPath);
                return;
            }

            Files.walk(dir)
                    .filter(path -> path.toString()
                            .endsWith(".java"))
                    .forEach(path -> processEntityFile(path.toString()));

        } catch (IOException e) {
            System.err.println("处理目录失败: " + entityDirPath + ", 错误: " + e.getMessage());
        }
    }

    public static void main(String[] args) {
        // 处理当前项目的实体类目录
        String projectPath = System.getProperty("user.dir");
        String entityDirPath = projectPath + "/src/main/java/com/example/entity";
        processEntityDirectory(entityDirPath);
    }
} 