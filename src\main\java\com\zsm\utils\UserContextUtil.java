package com.zsm.utils;

import com.zsm.model.vo.SaasUserInfoResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * 用户上下文工具类
 * 用于设置和获取线程上下文中的用户信息，支持在异步调用时传递用户信息
 *
 * <AUTHOR>
 * @date 2025/1/17
 */
@Slf4j
public class UserContextUtil {

    /**
     * 用户信息线程本地变量
     */
    private static final ThreadLocal<SaasUserInfoResponse> userInfoThreadLocal = new ThreadLocal<>();

    /**
     * 设置当前线程的用户信息
     *
     * @param userInfo 用户信息响应对象
     */
    public static void setCurrentUser(SaasUserInfoResponse userInfo) {
        if (userInfo != null) {
            userInfoThreadLocal.set(userInfo);
            log.info("设置线程上下文用户信息: {}", userInfo.getUser() != null ? userInfo.getUser().getUserName() : "未知用户");
        }
    }

    /**
     * 获取当前线程的用户信息
     *
     * @return 用户信息响应对象，如果未设置则返回null
     */
    public static SaasUserInfoResponse getCurrentUser() {
        return userInfoThreadLocal.get();
    }

    /**
     * 通过访问令牌设置用户信息
     * 该方法会主动调用SaaS接口获取用户信息并设置到线程上下文中
     *
     * @param authorization 访问令牌
     * @return 是否设置成功
     */
    public static boolean setCurrentUserByToken(String authorization) {
        try {
            if (authorization == null || authorization.trim().isEmpty()) {
                log.warn("访问令牌为空，无法获取用户信息");
                return false;
            }

            log.info("开始通过访问令牌获取用户信息");
            SaasUserInfoResponse userInfo = SaasHttpUtil.getInfo(authorization);
            
            if (userInfo == null) {
                log.error("通过访问令牌获取用户信息失败：响应为空");
                return false;
            }

            if (userInfo.getCode() != 200) {
                log.error("通过访问令牌获取用户信息失败，返回码：{}，消息：{}", 
                    userInfo.getCode(), userInfo.getMsg());
                return false;
            }

            // 设置访问令牌到用户信息对象中
            userInfo.setAuthorization(authorization);
            
            // 设置到线程上下文
            setCurrentUser(userInfo);
            
            log.info("成功设置用户信息到线程上下文，用户：{}", 
                userInfo.getUser() != null ? userInfo.getUser().getUserName() : "未知用户");
            return true;

        } catch (Exception e) {
            log.error("通过访问令牌设置用户信息时发生异常", e);
            return false;
        }
    }

    /**
     * 清除当前线程的用户信息
     */
    public static void clearCurrentUser() {
        userInfoThreadLocal.remove();
        log.debug("清除线程上下文用户信息");
    }

    /**
     * 检查当前线程是否设置了用户信息
     *
     * @return 如果设置了用户信息返回true，否则返回false
     */
    public static boolean hasCurrentUser() {
        SaasUserInfoResponse userInfo = userInfoThreadLocal.get();
        return userInfo != null && userInfo.getUser() != null;
    }

    /**
     * 获取当前用户ID
     *
     * @return 用户ID，如果未设置用户信息则返回null
     */
    public static String getCurrentUserId() {
        SaasUserInfoResponse userInfo = getCurrentUser();
        if (userInfo != null && userInfo.getUser() != null) {
            Long userId = userInfo.getUser().getUserId();
            return userId != null ? userId.toString() : null;
        }
        return null;
    }

    /**
     * 获取当前用户名
     *
     * @return 用户名，如果未设置用户信息则返回null
     */
    public static String getCurrentUserName() {
        SaasUserInfoResponse userInfo = getCurrentUser();
        if (userInfo != null && userInfo.getUser() != null) {
            return userInfo.getUser().getUserName();
        }
        return null;
    }

    /**
     * 获取当前用户昵称
     *
     * @return 用户昵称，如果未设置用户信息则返回null
     */
    public static String getCurrentNickName() {
        SaasUserInfoResponse userInfo = getCurrentUser();
        if (userInfo != null && userInfo.getUser() != null) {
            return userInfo.getUser().getNickName();
        }
        return null;
    }

    /**
     * 获取当前访问令牌
     *
     * @return 访问令牌，如果未设置用户信息则返回null
     */
    public static String getCurrentToken() {
        SaasUserInfoResponse userInfo = getCurrentUser();
        if (userInfo != null) {
            return userInfo.getAuthorization();
        }
        return null;
    }

    /**
     * 在指定的操作执行过程中保持用户上下文
     * 用于在异步调用或新线程中传递用户信息
     *
     * @param userInfo 要传递的用户信息
     * @param runnable 要执行的操作
     */
    public static void runWithUserContext(SaasUserInfoResponse userInfo, Runnable runnable) {
        SaasUserInfoResponse originalUser = getCurrentUser();
        try {
            setCurrentUser(userInfo);
            runnable.run();
        } finally {
            if (originalUser != null) {
                setCurrentUser(originalUser);
            } else {
                clearCurrentUser();
            }
        }
    }
}