package com.zsm.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 处理结果VO
 * 用于封装住院拆零确认处理的详细结果信息
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcessingResultVo {

    /**
     * 处理是否成功
     */
    private Boolean success;

    /**
     * 处理消息
     */
    private String message;

    /**
     * HIS接口获取的总数据量
     */
    private Integer hisDataCount;

    /**
     * 拆零药品数量（过滤后）
     */
    private Integer splitDrugCount;

    /**
     * 确认的拆药数量
     */
    private Integer confirmedCount;

    /**
     * 跳过的数量（已处理过的）
     */
    private Integer skippedCount;

    /**
     * 处理失败的数量
     */
    private Integer failedCount;

    /**
     * 账号处理详情
     */
    private List<AccountProcessingDetail> accountDetails;

    /**
     * 账号处理详情内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AccountProcessingDetail {
        
        /**
         * 账号用户名
         */
        private String username;
        
        /**
         * 账号描述
         */
        private String description;
        
        /**
         * 病区ID
         */
        private String wardId;
        
        /**
         * HIS获取数据量
         */
        private Integer hisDataCount;
        
        /**
         * 拆零药品数量
         */
        private Integer splitDrugCount;
        
        /**
         * 确认数量
         */
        private Integer confirmedCount;
        
        /**
         * 处理是否成功
         */
        private Boolean success;
        
        /**
         * 错误信息
         */
        private String errorMessage;
    }
} 