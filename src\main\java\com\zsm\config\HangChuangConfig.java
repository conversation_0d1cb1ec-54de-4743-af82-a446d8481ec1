package com.zsm.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 杭创接口配置类
 * 根据不同环境加载不同的IP地址和端口配置
 *
 * <AUTHOR>
 * @date 2025/01/03
 */
@Data
@Component
@ConfigurationProperties(prefix = "hangchuang")
public class HangChuangConfig {

    /**
     * 追溯码HIS接口服务器配置
     */
    private String server;

    /**
     * 互联网医院HIS接口服务器配置
     * 用于配置HIS系统提供的接口地址
     */
    private String hisServer;

    /**
     * 住院发药记录接口路径
     */
    private String inpatientDispenseRecord = "/api/TraceCode/inPatientDispenseRecord";

    /**
     * 住院发药明细接口路径
     */
    private String inpatientDispenseDetail = "/api/TraceCode/inPatientDispenseDetail";

    /**
     * HIS药品目录查询接口路径
     */
    private String hisDrugCatalog = "/api/Purchase/getHisDrugCatalog";

    /**
     * 获取患者信息接口路径
     */
    private String outpatientInfo = "/Leantech/OutPatient/getOutpatientInfo";

    /**
     * 门诊发药明细接口路径
     */
    private String outpatientDispenseDetail = "/api/TraceCode/outPatientDispenseDetail";

    /**
     * 出院患者列表接口路径
     */
    private String inpatientSettlement = "/api/TraceCode/queryInpatientSettlement";

    /**
     * 获取住院发药记录接口完整URL
     *
     * @return 完整URL
     */
    public String getInpatientDispenseRecordUrl() {
        return server + getInpatientDispenseRecord();
    }

    /**
     * 获取住院发药明细接口完整URL
     *
     * @return 完整URL
     */
    public String getInpatientDispenseDetailUrl() {
        return server + getInpatientDispenseDetail();
    }

    /**
     * 获取HIS药品目录查询接口完整URL
     *
     * @return 完整URL
     */
    public String getHisDrugCatalogUrl() {
        // HIS药品目录查询接口使用固定地址
        return server + getHisDrugCatalog();
    }

    /**
     * 获取患者信息接口完整URL
     *
     * @return 完整URL
     */
    public String getPatientInfoUrl() {
        return hisServer + getOutpatientInfo();
    }

    /**
     * 获取门诊发药明细接口完整URL
     *
     * @return 完整URL
     */
    public String getOutpatientDispenseDetailUrl() {
        return server + getOutpatientDispenseDetail();
    }

    /**
     * 获取出院患者列表接口完整URL
     *
     * @return 完整URL
     */
    public String getInpatientSettlementUrl() {
        return server + getInpatientSettlement();
    }
} 