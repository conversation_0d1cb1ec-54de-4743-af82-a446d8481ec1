# 项目技术框架文档

## 项目概述
基于 Spring Boot 2.7.5 和 MyBatis Plus 的企业级应用开发脚手架，提供完整的用户管理、多数据源切换、SOAP Web Service 调用和统一 API 响应格式等功能。

## 1. 主要技术框架和库

### 1.1 核心框架

| 框架/库 | 版本 | 类型 | 描述 |
|---------|------|------|------|
| **Spring Boot** | 2.7.5 | 核心框架 | 企业级应用开发框架，提供自动配置和起步依赖 |
| **Spring Web** | 2.7.5 | Web框架 | 构建RESTful API和Web应用 |
| **Spring AOP** | 2.7.5 | 切面编程 | 提供面向切面编程支持 |
| **Spring Validation** | 2.7.5 | 数据验证 | 数据校验和验证功能 |

### 1.2 数据访问层

| 框架/库 | 版本 | 类型 | 描述 |
|---------|------|------|------|
| **MyBatis Plus** | 3.5.3.1 | ORM框架 | 基于MyBatis的增强工具，简化CRUD操作 |
| **Dynamic Datasource** | 3.6.1 | 多数据源 | 动态数据源切换，支持读写分离 |
| **MySQL Connector** | 8.0.33 | 数据库驱动 | MySQL数据库连接驱动 |
| **Druid** | 1.2.16 | 连接池 | 阿里巴巴数据库连接池，提供监控功能 |
| **H2 Database** | - | 测试数据库 | 内存数据库，用于单元测试 |

### 1.3 工具库

| 框架/库 | 版本 | 类型 | 描述 |
|---------|------|------|------|
| **Lombok** | - | 代码简化 | 通过注解减少样板代码 |
| **Hutool** | 5.8.37 | 工具包 | Java工具类库，用于SOAP调用等 |

### 1.4 API文档

| 框架/库 | 版本 | 类型 | 描述 |
|---------|------|------|------|
| **SpringDoc OpenAPI** | 1.7.0 | API文档 | 基于OpenAPI 3.0的Swagger文档生成 |

### 1.5 代码生成

| 框架/库 | 版本 | 类型 | 描述 |
|---------|------|------|------|
| **MyBatis Plus Generator** | 3.5.3.1 | 代码生成器 | 自动生成Entity、Mapper、Service等代码 |
| **Velocity** | 2.3 | 模板引擎 | 代码生成器模板引擎 |
| **Freemarker** | 2.3.31 | 模板引擎 | 代码生成器备选模板引擎 |

### 1.6 任务调度

| 框架/库 | 版本 | 类型 | 描述 |
|---------|------|------|------|
| **Spring Task** | 2.7.5 | 任务调度 | Spring内置定时任务支持 |

## 2. 框架用途和作用

### 2.1 Spring Boot
- **主要用途**: 项目核心框架，提供自动配置和依赖管理
- **在项目中的作用**:
  - 应用启动和配置管理
  - 依赖注入和控制反转
  - 自动配置数据库连接、Web服务等
  - 提供开发、测试、生产环境配置

### 2.2 MyBatis Plus
- **主要用途**: 数据访问层ORM框架
- **在项目中的作用**:
  - 简化数据库CRUD操作
  - 提供分页、乐观锁、逻辑删除等功能
  - 自动填充创建时间、更新时间等字段
  - 代码生成器快速生成数据访问代码

### 2.3 Dynamic Datasource
- **主要用途**: 多数据源动态切换
- **在项目中的作用**:
  - 支持主从数据库配置
  - 读写分离
  - 通过注解方式切换数据源
  - 支持事务中的数据源管理

### 2.4 Druid
- **主要用途**: 数据库连接池
- **在项目中的作用**:
  - 管理数据库连接
  - 提供连接池监控页面
  - SQL执行统计和监控
  - 连接泄露检测

### 2.5 SpringDoc OpenAPI
- **主要用途**: API文档生成
- **在项目中的作用**:
  - 自动生成API文档
  - 提供Swagger UI界面
  - API测试和调试
  - 接口文档管理

## 3. 框架集成关系

```
Spring Boot 应用启动
    ├── Spring Web (RESTful API)
    │   ├── Controllers (接口层)
    │   ├── Validation (数据验证)
    │   └── SpringDoc OpenAPI (API文档)
    │
    ├── 业务服务层
    │   ├── Service Interfaces
    │   ├── Service Implementations
    │   └── Spring AOP (切面处理)
    │
    ├── 数据访问层
    │   ├── MyBatis Plus
    │   │   ├── Entity (实体类)
    │   │   ├── Mapper (数据访问)
    │   │   └── Plugins (分页、乐观锁等)
    │   │
    │   └── Dynamic Datasource
    │       ├── Master Database (主库)
    │       ├── Slave Databases (从库)
    │       └── Druid Connection Pool
    │
    ├── 工具组件
    │   ├── Hutool (SOAP调用)
    │   ├── Lombok (代码简化)
    │   └── Spring Task (定时任务)
    │
    └── 代码生成
        ├── MyBatis Plus Generator
        └── Template Engines (Velocity/Freemarker)
```

## 4. 各框架配置位置

### 4.1 主配置文件
- **位置**: `src/main/resources/application.yml`
- **内容**: 数据源配置、MyBatis Plus配置、日志配置、Swagger配置等

### 4.2 Java配置类
- **位置**: `src/main/java/com/example/config/`
- **配置类**:
  - `MybatisPlusConfig.java` - MyBatis Plus插件配置
  - `SwaggerConfig.java` - Swagger API文档配置
  - `MetaObjectHandlerConfig.java` - 自动填充配置
  - `CodeGeneratorConfig.java` - 代码生成器配置

### 4.3 启动类配置
- **位置**: `src/main/java/com/example/Application.java`
- **注解配置**:
  - `@SpringBootApplication` - Spring Boot启动
  - `@MapperScan` - MyBatis Mapper扫描
  - `@EnableScheduling` - 定时任务启用

### 4.4 Maven配置
- **位置**: `pom.xml`
- **内容**: 依赖版本管理、插件配置、构建配置

## 5. 框架使用示例

### 5.1 Spring Boot Controller 示例

```java
@Slf4j
@Validated
@RestController
@RequestMapping("/users")
@RequiredArgsConstructor
@Tag(name = "用户管理", description = "用户相关的增删改查操作接口")
public class UserController {

    private final UserService userService;

    @Operation(summary = "分页查询用户列表")
    @GetMapping
    public ApiResult<IPage<User>> getUserPage(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size) {
        
        Page<User> page = new Page<>(current, size);
        IPage<User> userPage = userService.getUserPage(page, null, null);
        
        return ApiResult.success("查询成功", userPage);
    }
}
```

### 5.2 MyBatis Plus 实体类示例

```java
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("user")
@ApiModel(description = "用户实体")
public class User extends BaseEntity {

    @ApiModelProperty(value = "用户名", required = true, example = "admin")
    @NotBlank(message = "用户名不能为空")
    @Length(min = 2, max = 20, message = "用户名长度必须在2-20个字符之间")
    @TableField("username")
    private String username;

    @ApiModelProperty(value = "用户状态", example = "1")
    @TableField("status")
    private Integer status;
}
```

### 5.3 MyBatis Plus Mapper 示例

```java
@Mapper
public interface UserMapper extends BaseMapper<User> {
    
    /**
     * 根据用户名查询用户
     */
    @Select("SELECT * FROM user WHERE username = #{username} AND deleted = 0")
    User selectByUsername(@Param("username") String username);
    
    /**
     * 查询活跃用户列表
     */
    List<User> selectActiveUsers();
}
```

### 5.4 MyBatis Plus 服务类示例

```java
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    @Override
    public IPage<User> getUserPage(Page<User> page, String username, Integer status) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.isNotBlank(username), User::getUsername, username)
               .eq(status != null, User::getStatus, status)
               .orderByDesc(User::getCreateTime);
        
        return this.page(page, wrapper);
    }
}
```

### 5.5 多数据源使用示例

```java
@Service
@RequiredArgsConstructor
public class MultiDataSourceDemoServiceImpl implements MultiDataSourceDemoService {

    private final UserMapper userMapper;

    @DS("master")  // 使用主数据源
    @Override
    public List<User> getUsersFromMaster() {
        return userMapper.selectList(null);
    }

    @DS("slave_1")  // 使用从数据源1
    @Override
    public List<User> getUsersFromSlave1() {
        return userMapper.selectList(null);
    }
}
```

### 5.6 自动填充配置示例

```java
@Component
@Slf4j
public class MetaObjectHandlerConfig implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        log.info("start insert fill ....");
        this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, LocalDateTime.now());
        this.strictInsertFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
        this.strictInsertFill(metaObject, "createBy", String.class, getCurrentUser());
        this.strictInsertFill(metaObject, "updateBy", String.class, getCurrentUser());
        this.strictInsertFill(metaObject, "deleted", Integer.class, 0);
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        log.info("start update fill ....");
        this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
        this.strictUpdateFill(metaObject, "updateBy", String.class, getCurrentUser());
    }
}
```

### 5.7 统一API响应格式示例

```java
@Data
@ApiModel(description = "统一API响应格式")
public class ApiResult<T> implements Serializable {

    @ApiModelProperty(value = "状态码", example = "200")
    private Integer code;

    @ApiModelProperty(value = "响应消息", example = "操作成功")
    private String msg;

    @ApiModelProperty(value = "响应数据")
    private T data;

    @ApiModelProperty(value = "时间戳", example = "1640995200000")
    private Long timestamp;

    public static <T> ApiResult<T> success(String message, T data) {
        return new ApiResult<>(200, message, data);
    }

    public static <T> ApiResult<T> error(Integer code, String message) {
        return new ApiResult<>(code, message, null);
    }
}
```

### 5.8 定时任务示例

```java
@Component
@Slf4j
public class ScheduledTasks {

    @Scheduled(fixedRate = 60000) // 每分钟执行一次
    public void reportCurrentTime() {
        log.info("当前时间: {}", LocalDateTime.now());
    }

    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void dailyTask() {
        log.info("执行每日任务");
    }
}
```

## 6. 开发和部署配置

### 6.1 开发环境配置
- **数据库**: MySQL 8.0+
- **JDK**: 1.8+
- **Maven**: 3.6+
- **IDE**: IntelliJ IDEA / Eclipse

### 6.2 关键配置文件路径
- 主配置: `application.yml`
- 测试配置: `application-test.yml`
- Maven配置: `pom.xml`
- 数据库脚本: `src/main/resources/sql/schema.sql`
- Mapper XML: `src/main/resources/mapper/`

### 6.3 监控和调试
- **Druid监控**: http://localhost:8080/api/druid (admin/123456)
- **Swagger文档**: http://localhost:8080/api/swagger-ui.html
- **API文档**: http://localhost:8080/api/v3/api-docs

## 7. 扩展和自定义

### 7.1 添加新的数据源
1. 在 `application.yml` 中配置新数据源
2. 使用 `@DS("dataSourceName")` 注解切换
3. 确保事务管理正确配置

### 7.2 自定义MyBatis Plus插件
1. 实现 `InnerInterceptor` 接口
2. 在 `MybatisPlusConfig` 中注册插件
3. 配置插件执行顺序

### 7.3 扩展API响应格式
1. 修改 `ApiResult` 类添加新字段
2. 更新相关的静态工厂方法
3. 同步更新API文档注解

本技术框架文档为项目开发提供了完整的技术栈概览和使用指南，有助于开发团队快速理解和使用项目中的各种技术框架。 