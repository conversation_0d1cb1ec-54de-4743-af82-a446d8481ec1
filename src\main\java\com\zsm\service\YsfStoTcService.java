package com.zsm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zsm.entity.YsfStoTc;
import com.zsm.model.vo.TraceabilityCodeInfoVo;

/**
 * <p>
 * 商品追溯码 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
public interface YsfStoTcService extends IService<YsfStoTc> {

    /**
     * 根据追溯码查询其信息和生命周期
     *
     * @param drugtracinfo 追溯码
     * @return 追溯码信息和生命周期记录
     */
    TraceabilityCodeInfoVo getByDrugtracinfo(String drugtracinfo);

}
