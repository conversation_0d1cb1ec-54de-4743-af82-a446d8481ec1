package com.zsm.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Size;
import java.util.List;

/**
 * 药品追溯码上传请求类
 *
 * <AUTHOR>
 * @date 2025/5/15 下午2:36
 */
@Data
@Schema(description = "药品追溯码上传请求类")
public class TraceabilityUploadDto {

    /**
     * 退药单标记;0: 发药, 1: 退药
     */
    @Schema(description = "退药单标记;0: 发药, 1: 退药")
    private String fgDps;
    /**
     * 发药单类型;1: 住院处方, 2:门诊处方
     */
    @Schema(description = "发药单类型;1: 住院处方, 2:门诊处方")
    private String sdDps;
    /**
     * 患者病区ID
     */
    @Schema(description = "患者病区ID")
    @Size(max = 100, message = "患者病区ID长度不能超过100个字符")
    private String patWardId;

    /**
     * 患者病区名称
     */
    @Schema(description = "患者病区名称")
    @Size(max = 100, message = "患者病区名称长度不能超过100个字符")
    private String patWardName;

    /**
     * 窗口
     */
    @Schema(description = "窗口")
    private String window;

    /**
     * 选择退药操作员ID
     */
    @Schema(description = "选择退药操作员ID")
    private String selRetnOpterId;
    /**
     * 处方ID列表
     */
    @Schema(description = "处方ID列表")
    private List<PrescriptionItem> prescriptions;

    /**
     * 处方项目明细
     */
    @Data
    @Schema(description = "处方项目明细")
    public static class PrescriptionItem {
        /**
         * 发送时间
         */
        @Schema(description = "发送时间")
        private String sendTime;
        /**
         * 处方ID
         */
        @Schema(description = "处方ID")
        private String outPresId;

        /**
         * 处方编号
         */
        @Schema(description = "处方编号")
        private String presCode;

        /**
         * 患者ID
         */
        @Schema(description = "患者ID")
        private String patId;

        /**
         * 患者姓名
         */
        @Schema(description = "患者姓名")
        private String patName;
        /**
         * 就诊卡号
         */
        @Schema(description = "就诊卡号")
        private String cardNo;
        /**
         * 发药科室id
         */
        @Schema(description = "发药科室id")
        private String idDept;

        /**
         * 药品明细列表
         */
        @Schema(description = "药品明细列表")
        private List<DrugItem> drugItems;
    }

    /**
     * 药品明细项
     */
    @Data
    @Schema(description = "药品明细项")
    public static class DrugItem {
        /**
         * 处方明细ID
         */
        @Schema(description = "处方明细ID")
        private String outPresdetailid;

        /**
         * 药品编码
         */
        @Schema(description = "药品编码")
        private String drugCode;

        /**
         * 药品名称
         */
        @Schema(description = "药品名称")
        private String drugName;

        /**
         * 药品规格
         */
        @Schema(description = "药品规格")
        private String spec;

        /**
         * 生产企业
         */
        @Schema(description = "生产企业")
        private String prodentpName;

        /**
         * 药品数量
         */
        @Schema(description = "药品数量")
        private Integer quantity;

        /**
         * 药品单位
         */
        @Schema(description = "药品单位")
        private String unit;

        /**
         * 药品单价
         */
        @Schema(description = "药品单价")
        private Double price;

        /**
         * 药品总价
         */
        @Schema(description = "药品总价")
        private Double amount;

        /**
         * 扫描的追溯码
         */
        @Schema(description = "扫描的追溯码")
        private String drugtracinfoScanned;

        /**
         * 最小制剂单位数量
         */
        @Schema(description = "最小制剂单位数量")
        private Integer minDoseCount;

        /**
         * 最小包装名称
         */
        @Schema(description = "最小包装名称")
        private String minPackingName;
        /**
         * 拆零标识:0未拆零,1拆零
         */
        @Schema(description = "拆零标识:0未拆零,1拆零")
        private Integer trdnFlag;
        /**
         * 发药数量
         */
        @Schema(description = "发药数量")
        private Integer dispCnt;
        /**
         * 处方号
         */
        @Schema(description = "处方号")
        private String cfxh;
        /**
         * 处方明细号
         */
        @Schema(description = "处方明细号")
        private String cfmxxh;
        /**
         * 原发药单处方明细序号;用于退药单
         */
        @Schema(description = "原发药单处方明细序号;用于退药单")
        private String oriCfmxxh;

        /**
         * 原发药单明细ID;用于退药单，原发药单明细id
         */
        @Schema(description = "原发药单明细ID;用于退药单，原发药单明细id")
        private String oriId;
    }
}
