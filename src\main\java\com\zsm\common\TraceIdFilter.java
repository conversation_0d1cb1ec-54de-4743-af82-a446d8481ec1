package com.zsm.common;

import org.slf4j.MDC;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import jakarta.servlet.*;
import jakarta.servlet.annotation.WebFilter;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.UUID;

/**
 * 链路追踪过滤器
 * 为每个请求生成唯一的traceId，用于日志追踪
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Component
@WebFilter(urlPatterns = "/*", filterName = "traceIdFilter")
@Order(1)
public class TraceIdFilter implements Filter {

    private static final String TRACE_ID = "traceId";
    private static final String TRACE_ID_HEADER = "X-Trace-Id";

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        // 初始化方法，可以在这里进行一些初始化操作
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        
        try {
            // 尝试从请求头中获取traceId
            String traceId = httpRequest.getHeader(TRACE_ID_HEADER);
            
            // 如果请求头中没有traceId，则生成一个新的
            if (traceId == null || traceId.trim().isEmpty()) {
                traceId = generateTraceId();
            }
            
            // 将traceId放入MDC中，供日志使用
            MDC.put(TRACE_ID, traceId);
            
            // 将traceId添加到响应头中
            httpResponse.setHeader(TRACE_ID_HEADER, traceId);
            
            // 继续执行后续的过滤器和处理器
            chain.doFilter(request, response);
            
        } finally {
            // 请求处理完成后，清除MDC中的traceId，避免内存泄漏
            MDC.clear();
        }
    }

    @Override
    public void destroy() {
        // 销毁方法，可以在这里进行一些清理操作
    }

    /**
     * 生成唯一的traceId
     * 
     * @return 生成的traceId
     */
    private String generateTraceId() {
        // 使用UUID生成唯一标识，去掉横线并取前16位
        return UUID.randomUUID().toString().replace("-", "").substring(0, 16);
    }
} 