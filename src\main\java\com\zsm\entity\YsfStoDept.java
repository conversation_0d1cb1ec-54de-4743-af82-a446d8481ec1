package com.zsm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 仓储定义实体类
 * 
 * <AUTHOR>
 * @since 2025-06-02
 */
@Data
@TableName("ysf_sto_dept")
@Schema(description = "仓储定义")
public class YsfStoDept implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 仓储主键
     */
    @Schema(description = "仓储主键")
    @TableId(value = "id_sto", type = IdType.AUTO)
    private Long idSto;

    /**
     * 仓储类型;phis.sto.stoType:1:药库,2:药房,3:库房,4科室库房
     */
    @Schema(description = "仓储类型;phis.sto.stoType:1:药库,2:药房,3:库房,4科室库房")
    @Size(max = 100, message = "仓储类型;phis.sto.stoType:1:药库,2:药房,3:库房,4科室库房长度不能超过100个字符")
    @TableField("sd_sto")
    private String sdSto;

    /**
     * 仓储物品类型;rbmh.base.med.articleType,1西药、2中成药、3草药、4疫苗、5耗材、6固定资产,7.民族药品,8.院内制剂
     */
    @Schema(description = "仓储物品类型;rbmh.base.med.articleType,1西药、2中成药、3草药、4疫苗、5耗材、6固定资产,7.民族药品,8.院内制剂")
    @Size(max = 100, message = "仓储物品类型;rbmh.base.med.articleType,1西药、2中成药、3草药、4疫苗、5耗材、6固定资产,7.民族药品,8.院内制剂长度不能超过100个字符")
    @TableField("sds_sto_pro")
    private String sdsStoPro;

    /**
     * 仓储应用场景;phis.sto.stoScene:1:门诊,2:住院,9:全部
     */
    @Schema(description = "仓储应用场景;phis.sto.stoScene:1:门诊,2:住院,9:全部")
    @Size(max = 100, message = "仓储应用场景;phis.sto.stoScene:1:门诊,2:住院,9:全部长度不能超过100个字符")
    @TableField("sd_sto_scene")
    private String sdStoScene;

    /**
     * 仓储名称
     */
    @Schema(description = "仓储名称")
    @Size(max = 100, message = "仓储名称长度不能超过100个字符")
    @TableField("na_sto")
    private String naSto;

    /**
     * 部门编码;bbp部门
     */
    @Schema(description = "部门编码;bbp部门")
    @Size(max = 100, message = "部门编码;bbp部门长度不能超过100个字符")
    @TableField("id_dept")
    private String idDept;

    /**
     * 盘点处理方式;phis.sto.checkway:1.生成出入库单,2直接增减库存
     */
    @Schema(description = "盘点处理方式;phis.sto.checkway:1.生成出入库单,2直接增减库存")
    @Size(max = 100, message = "盘点处理方式;phis.sto.checkway:1.生成出入库单,2直接增减库存长度不能超过100个字符")
    @TableField("sd_checkway")
    private String sdCheckway;

    /**
     * 包装管理方式;phis.sto.packagway:1.默认大包装,2默认小包装
     */
    @Schema(description = "包装管理方式;phis.sto.packagway:1.默认大包装,2默认小包装")
    @Size(max = 100, message = "包装管理方式;phis.sto.packagway:1.默认大包装,2默认小包装长度不能超过100个字符")
    @TableField("sd_packagway")
    private String sdPackagway;

    /**
     * 有效标识;phis.vali_flag:1有效,0无效
     */
    @Schema(description = "有效标识;phis.vali_flag:1有效,0无效")
    @Size(max = 100, message = "有效标识;phis.vali_flag:1有效,0无效长度不能超过100个字符")
    @TableField("fg_active")
    private String fgActive;

    /**
     * 云药房;允许任意机构开方
     */
    @Schema(description = "云药房;允许任意机构开方")
    @Size(max = 100, message = "云药房;允许任意机构开方长度不能超过100个字符")
    @TableField("fg_cloud")
    private String fgCloud;

    /**
     * 出库顺序;phis.sto.orderType,多选
     */
    @Schema(description = "出库顺序;phis.sto.orderType,多选")
    @Size(max = 100, message = "出库顺序;phis.sto.orderType,多选长度不能超过100个字符")
    @TableField("sds_order")
    private String sdsOrder;

    /**
     * 特殊标志;phis.sto.specialType,多选
     */
    @Schema(description = "特殊标志;phis.sto.specialType,多选")
    @Size(max = 100, message = "特殊标志;phis.sto.specialType,多选长度不能超过100个字符")
    @TableField("sds_special")
    private String sdsSpecial;

    /**
     * 机构编号
     */
    @Schema(description = "机构编号")
    @Size(max = 100, message = "机构编号长度不能超过100个字符")
    @TableField("id_org")
    private String idOrg;

    /**
     * 医疗机构编码
     */
    @Schema(description = "医疗机构编码")
    @Size(max = 100, message = "医疗机构编码长度不能超过100个字符")
    @TableField("org_id")
    private String orgId;

    /**
     * 医疗机构名称
     */
    @Schema(description = "医疗机构名称")
    @NotBlank(message = "医疗机构名称不能为空")
    @Size(max = 30, message = "医疗机构名称长度不能超过30个字符")
    @TableField("org_name")
    private String orgName;

    /**
     * 乐观锁
     */
    @Schema(description = "乐观锁")
    @Size(max = 100, message = "乐观锁长度不能超过100个字符")
    @TableField("revision")
    private String revision;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    @Size(max = 100, message = "创建者长度不能超过100个字符")
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    @Size(max = 100, message = "更新者长度不能超过100个字符")
    @TableField("update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @TableField("remark")
    private String remark;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @Schema(description = "删除标志（0代表存在 1代表删除）")
    @Size(max = 100, message = "删除标志（0代表存在 1代表删除）长度不能超过100个字符")
    @TableField("del_flag")
    private String delFlag;
} 
