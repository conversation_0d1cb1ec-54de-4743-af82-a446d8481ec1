package com.zsm.model.saas.response;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class QueryTracDrugResponse {

    private String drugCode;
    private String cfxh;
    private String cfmxxh;
    private String isTrac; // 是否拆零：0=否 ,1=是
    private BigDecimal conRatio;

    /*
      "drugCode": "D1403",
            "isTrac": 0,
            "conRatio": 50.0000,
            "cfxh": "1001",
            "cfmxxh": "1001001"
     */
}