<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsm.mapper.Nhsa3505Mapper">

    <select id="getByCfxhandDrugCode" resultType="com.zsm.entity.Nhsa3505">
        select *
        from nhsa_3505
        where out_pres_id = #{cfxh}
        and fixmedins_hilist_id = #{drugCode}
        and hsa_sync_status = '0'
    </select>


    <select id="getByBchno" resultType="com.zsm.entity.Nhsa3505">
        select *
        from nhsa_3505
        where fixmedins_bchno = #{outPresdetailid}
    </select>

    <select id="getFirstByDrugCodeWithSyncStatus" resultType="com.zsm.entity.Nhsa3505">
        select *
        from nhsa_3505
        where fixmedins_hilist_id = #{drugCode}
        and hsa_sync_status = '0'
        order by id desc
        limit 1
    </select>
</mapper>
