<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsm.mapper.YsfStoDpsSubMapper">
    <select id="getSendStatusList" resultType="com.zsm.entity.YsfStoDpsSub">
        select sub.*
        from ysf_sto_dps_sub sub
                 left join ysf_sto_dps dps on dps.id_dps = sub.id_dps
        where dps.fg_status = '1'
          and sub.del_flag = '0'
          and dps.del_flag = '0'
          and DATE_FORMAT(sub.create_time, '%Y-%m-%d') = DATE_FORMAT(NOW(), '%Y-%m-%d')
    </select>

    <!-- 根据条件查询发药单明细列表 -->
    <select id="queryDispensingRecordDetailsWithConditions"
            parameterType="com.zsm.model.dto.DispensingRecordDetailQueryDto"
            resultType="com.zsm.entity.YsfStoDpsSub">
        SELECT
        sub.*
        FROM ysf_sto_dps_sub sub
        <where>
            <if test="cfxh != null and cfxh != ''">
                AND sub.cfxh = #{cfxh}
            </if>
            <!-- 必须的发药单ID条件 -->
            <if test="idDps != null and idDps != ''">
                AND sub.id_dps = #{idDps}
            </if>

            <!-- 药品名称或编码条件 -->
            <if test="drugNameOrCode != null and drugNameOrCode != ''">
                AND (
                sub.na_fee LIKE CONCAT('%', #{drugNameOrCode}, '%')
                OR sub.drug_code LIKE CONCAT('%', #{drugNameOrCode}, '%')
                )
            </if>

            <!-- 过滤未删除的记录 -->
            AND sub.del_flag = '0'
        </where>
        ORDER BY sub.id DESC
    </select>
</mapper>
