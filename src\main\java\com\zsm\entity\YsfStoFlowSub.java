package com.zsm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 仓储物品出入库明细实体类
 * 
 * <AUTHOR>
 * @since 2025-06-02
 */
@Data
@TableName("ysf_sto_flow_sub")
@Schema(description = "仓储物品出入库明细")
public class YsfStoFlowSub implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 出入库明细ID
     */
    @Schema(description = "出入库明细ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 出入库主键
     */
    @Schema(description = "出入库主键")
    @Size(max = 100, message = "出入库主键长度不能超过100个字符")
    @TableField("id_sto_flow")
    private String idStoFlow;

    /**
     * 商品编码
     */
    @Schema(description = "商品编码")
    @Size(max = 100, message = "商品编码长度不能超过100个字符")
    @TableField("drug_code")
    private String drugCode;

    /**
     * 发票号码
     */
    @Schema(description = "发票号码")
    @Size(max = 100, message = "发票号码长度不能超过100个字符")
    @TableField("sn_bill")
    private String snBill;

    /**
     * 进货价格;调拨和申领都以出库方信息为准
     */
    @Schema(description = "进货价格;调拨和申领都以出库方信息为准")
    @TableField("price_pur")
    private BigDecimal pricePur;

    /**
     * 零售价格;调拨和申领都以出库方信息为准
     */
    @Schema(description = "零售价格;调拨和申领都以出库方信息为准")
    @TableField("price_sale")
    private BigDecimal priceSale;

    /**
     * 出入库数量
     */
    @Schema(description = "出入库数量")
    @TableField("sel_retn_cnt")
    private Integer selRetnCnt;

    /**
     * 出入库包装单位
     */
    @Schema(description = "出入库包装单位")
    @Size(max = 100, message = "出入库包装单位长度不能超过100个字符")
    @TableField("unit_flow")
    private String unitFlow;

    /**
     * 出入库包装转换系数
     */
    @Schema(description = "出入库包装转换系数")
    @TableField("unit_sale_factor")
    private Integer unitSaleFactor;

    /**
     * 进货合计
     */
    @Schema(description = "进货合计")
    @TableField("amt_total_pur")
    private BigDecimal amtTotalPur;

    /**
     * 零售合计
     */
    @Schema(description = "零售合计")
    @TableField("amt_total")
    private BigDecimal amtTotal;

    /**
     * 请求数量
     */
    @Schema(description = "请求数量")
    @TableField("amount_request")
    private Integer amountRequest;

    /**
     * 请求包装单位
     */
    @Schema(description = "请求包装单位")
    @Size(max = 100, message = "请求包装单位长度不能超过100个字符")
    @TableField("unit_flow_request")
    private String unitFlowRequest;

    /**
     * 请求包装转换系数
     */
    @Schema(description = "请求包装转换系数")
    @TableField("unit_sale_factor_request")
    private Integer unitSaleFactorRequest;

    /**
     * 药品批次
     */
    @Schema(description = "药品批次")
    @Size(max = 100, message = "药品批次长度不能超过100个字符")
    @TableField("manu_lotnum")
    private String manuLotnum;

    /**
     * 药品效期
     */
    @Schema(description = "药品效期")
    @TableField("expy_end")
    private LocalDateTime expyEnd;

    /**
     * 扩展字段
     */
    @Schema(description = "扩展字段")
    @Size(max = 100, message = "扩展字段长度不能超过100个字符")
    @TableField("json_field")
    private String jsonField;

    /**
     * 库存id;发起方库存id
     */
    @Schema(description = "库存id;发起方库存id")
    @Size(max = 100, message = "库存id;发起方库存id长度不能超过100个字符")
    @TableField("id_sto_inv")
    private String idStoInv;

    /**
     * 目标库房库存id;目标库房库存id
     */
    @Schema(description = "目标库房库存id;目标库房库存id")
    @Size(max = 100, message = "目标库房库存id;目标库房库存id长度不能超过100个字符")
    @TableField("id_sto_inv_target")
    private String idStoInvTarget;

    /**
     * 目标库房商品id;相同的药品有多个不同的包装,一个包装一个商品.跨部门库存移动时可能存在不同包装的商品移动,故记录目标库房商品id
     */
    @Schema(description = "目标库房商品id;相同的药品有多个不同的包装,一个包装一个商品.跨部门库存移动时可能存在不同包装的商品移动,故记录目标库房商品id")
    @Size(max = 100, message = "目标库房商品id;相同的药品有多个不同的包装,一个包装一个商品.跨部门库存移动时可能存在不同包装的商品移动,故记录目标库房商品id长度不能超过100个字符")
    @TableField("id_med_pro_target")
    private String idMedProTarget;

    /**
     * 验收明细id
     */
    @Schema(description = "验收明细id")
    @Size(max = 100, message = "验收明细id长度不能超过100个字符")
    @TableField("id_flow_accept_sub")
    private String idFlowAcceptSub;

    /**
     * 加成率
     */
    @Schema(description = "加成率")
    @TableField("per")
    private BigDecimal per;

    /**
     * 排序号
     */
    @Schema(description = "排序号")
    @TableField("sn")
    private Integer sn;

    /**
     * 机构编号
     */
    @Schema(description = "机构编号")
    @Size(max = 100, message = "机构编号长度不能超过100个字符")
    @TableField("id_org")
    private String idOrg;

    /**
     * 医疗机构编码
     */
    @Schema(description = "医疗机构编码")
    @Size(max = 100, message = "医疗机构编码长度不能超过100个字符")
    @TableField("org_id")
    private String orgId;

    /**
     * 医疗机构名称
     */
    @Schema(description = "医疗机构名称")
    @NotBlank(message = "医疗机构名称不能为空")
    @Size(max = 30, message = "医疗机构名称长度不能超过30个字符")
    @TableField("org_name")
    private String orgName;

    /**
     * 乐观锁
     */
    @Schema(description = "乐观锁")
    @Size(max = 100, message = "乐观锁长度不能超过100个字符")
    @TableField("revision")
    private String revision;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    @Size(max = 100, message = "创建者长度不能超过100个字符")
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    @Size(max = 100, message = "更新者长度不能超过100个字符")
    @TableField("update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @TableField("remark")
    private String remark;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @Schema(description = "删除标志（0代表存在 1代表删除）")
    @Size(max = 100, message = "删除标志（0代表存在 1代表删除）长度不能超过100个字符")
    @TableField("del_flag")
    private String delFlag;
} 
