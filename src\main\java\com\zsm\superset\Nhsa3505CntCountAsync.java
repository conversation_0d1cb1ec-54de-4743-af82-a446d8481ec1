package com.zsm.superset;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zsm.entity.Nhsa3505;
import com.zsm.entity.Nhsa3505CntCount;
import com.zsm.mapper.Nhsa3505CntCountMapper;
import com.zsm.mapper.Nhsa3505Mapper;
import com.zsm.utils.SuperSetUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 医保3505接口数据统计异步处理组件
 * <p>
 * 该组件负责统计医保3505接口的各项数据指标，包括：
 * - 总数据量统计
 * - 追溯码数据量统计
 * - 销售退货数量统计
 * - 追溯码个数统计
 * 统计结果会保存到数据库并上传到SuperSet进行数据可视化
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
@Slf4j
public class Nhsa3505CntCountAsync {

    @Resource
    private Nhsa3505Mapper nhsa3505Mapper;

    @Resource
    private Nhsa3505CntCountMapper nhsa3505CntCountMapper;

    /**
     * 统计医保3505接口数据并上传到SuperSet
     * <p>
     * 该方法是主要的业务入口，执行以下操作：
     * 1. 获取所有医疗机构列表
     * 2. 遍历每个医疗机构，获取其数据日期列表
     * 3. 对每个医疗机构的每个日期进行数据统计
     * 4. 统计包括：总数据量、追溯码数据量、销售退货总数量、追溯码总个数
     * 5. 将统计结果保存或更新到数据库
     * 6. 最后将所有统计数据上传到SuperSet进行数据可视化
     * </p>
     * 
     * @throws RuntimeException 当数据库操作失败或SuperSet上传失败时抛出
     */
    public void countNhsa3505CntCount() {
        List<Nhsa3505> medicalList = this.getNhsa3505MedicalList();

        for (Nhsa3505 nhsa3505 : medicalList) {
            String medicalCode = nhsa3505.getMedicalCode();
            String medicalName = nhsa3505.getMedicalName();
            List<Object> dateList = this.getNhsa3505DateList(medicalCode);
            for (Object dateObj : dateList) {
                String date = String.valueOf(dateObj);
                Nhsa3505CntCount count = new Nhsa3505CntCount();
                count.setMedicalCode(medicalCode);
                count.setMedicalName(medicalName);
                count.setCountDate(date);
                count.setTotalCount(this.getNhsa3505Total(medicalCode, date));
                count.setTraceCodeCount(this.getNhsa3505TraceCodeTotal(medicalCode, date));
                count.setAllCntCount(this.getNhsa3505AllCnt(medicalCode, date));
                count.setAllDataNumber(this.getNhsa3505AllDataNumber(medicalCode, date));
                count.setTraceCodeDataNumber(this.getNhsa3505TraceCodeDataNumber(medicalCode, date));
                this.saveOrUpdateNhsa3505CntCount(count);
            }
        }
        final List<Nhsa3505CntCount> nhsa3505CntCounts = nhsa3505CntCountMapper.selectNhsa3505CntCountList(new Nhsa3505CntCount());
        log.info("开始统计医保3505接口数据,上传SuperSet平台数据:{}", JSONUtil.toJsonStr(nhsa3505CntCounts));
        SuperSetUtil.uploadNhsa3505CntCountList(nhsa3505CntCounts);
    }

    /**
     * 根据日期范围统计医保3505接口数据并上传到SuperSet
     * @param startDate 开始日期，格式：yyyy-MM-dd，为null时默认为今天
     * @param endDate 结束日期，格式：yyyy-MM-dd，为null时默认为今天
     */
    public void countNhsa3505CntCount(String startDate, String endDate) {
        // 如果没有传入日期参数，默认统计今天的数据
        if (startDate == null || startDate.trim().isEmpty()) {
            startDate = LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE);
        }
        if (endDate == null || endDate.trim().isEmpty()) {
            endDate = LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE);
        }
        
        List<Nhsa3505> medicalList = this.getNhsa3505MedicalList();

        for (Nhsa3505 nhsa3505 : medicalList) {
            String medicalCode = nhsa3505.getMedicalCode();
            String medicalName = nhsa3505.getMedicalName();
            List<Object> dateList = this.getNhsa3505DateListByRange(medicalCode, startDate, endDate);
            for (Object dateObj : dateList) {
                String date = String.valueOf(dateObj);
                Nhsa3505CntCount count = new Nhsa3505CntCount();
                count.setMedicalCode(medicalCode);
                count.setMedicalName(medicalName);
                count.setCountDate(date);
                count.setTotalCount(this.getNhsa3505Total(medicalCode, date));
                count.setTraceCodeCount(this.getNhsa3505TraceCodeTotal(medicalCode, date));
                count.setAllCntCount(this.getNhsa3505AllCnt(medicalCode, date));
                count.setAllDataNumber(this.getNhsa3505AllDataNumber(medicalCode, date));
                count.setTraceCodeDataNumber(this.getNhsa3505TraceCodeDataNumber(medicalCode, date));
                this.saveOrUpdateNhsa3505CntCount(count);
            }
        }
        
        // 只查询指定日期范围内的统计数据上传
        // 需要在mapper中添加日期范围查询支持
        final List<Nhsa3505CntCount> nhsa3505CntCounts = nhsa3505CntCountMapper.selectNhsa3505CntCountListByDateRange(startDate, endDate);
        log.info("开始统计医保3505接口数据(日期范围：{} 至 {})，上传SuperSet平台数据:{}", startDate, endDate, JSONUtil.toJsonStr(nhsa3505CntCounts));
        SuperSetUtil.uploadNhsa3505CntCountList(nhsa3505CntCounts);
    }

    /**
     * 保存或更新医保3505统计数据
     * <p>
     * 根据医疗机构代码和统计日期查询是否已存在统计记录：
     * - 如果不存在，则新增记录
     * - 如果已存在，则更新记录（保留原有ID）
     * 使用synchronized关键字确保并发安全
     * </p>
     *
     * @param count 待保存或更新的统计数据对象
     * @throws RuntimeException 当数据库操作失败时抛出
     */
    private synchronized void saveOrUpdateNhsa3505CntCount(Nhsa3505CntCount count) {
        QueryWrapper<Nhsa3505CntCount> qw = new QueryWrapper<>();
        qw.eq("medical_code", count.getMedicalCode());
        qw.eq("count_date", count.getCountDate());
        Nhsa3505CntCount data = nhsa3505CntCountMapper.selectOne(qw);
        if (ObjectUtils.isEmpty(data)) {
            nhsa3505CntCountMapper.insert(count);
        } else {
            BeanUtil.copyProperties(count, data, "id");
            nhsa3505CntCountMapper.updateById(data);
        }
    }

    /**
     * 获取医保3505数据中的所有医疗机构列表
     * <p>
     * 通过GROUP BY医疗机构代码去重，获取系统中所有有数据的医疗机构信息
     * </p>
     *
     * @return 医疗机构列表，每个医疗机构包含机构代码和机构名称
     * @throws RuntimeException 当数据库查询失败时抛出
     */
    private List<Nhsa3505> getNhsa3505MedicalList() {
        QueryWrapper<Nhsa3505> qw = new QueryWrapper<>();
        qw.groupBy("medical_code");
        return nhsa3505Mapper.selectList(qw);
    }

    /**
     * 获取指定医疗机构的数据日期列表
     * <p>
     * 查询指定医疗机构在系统中有数据的所有日期，按日期倒序排列
     * 使用DATE函数提取创建时间的日期部分进行分组
     * </p>
     *
     * @param medicalCode 医疗机构代码
     * @return 该医疗机构的数据日期列表，格式为YYYY-MM-DD，按日期倒序排列
     * @throws RuntimeException 当数据库查询失败时抛出
     */
    private List<Object> getNhsa3505DateList(String medicalCode) {
        QueryWrapper<Nhsa3505> qw = new QueryWrapper<>();
        qw.eq("medical_code", medicalCode);
        qw.select("date(create_time)");
        qw.groupBy("date(create_time)");
        qw.orderByDesc("date(create_time)");
        List<Object> objects = nhsa3505Mapper.selectObjs(qw);
        return objects;
    }

    /**
     * 获取指定医疗机构在日期范围内的数据日期列表
     * @param medicalCode 医疗机构代码
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 该医疗机构的数据日期列表，格式为YYYY-MM-DD，按日期倒序排列
     */
    private List<Object> getNhsa3505DateListByRange(String medicalCode, String startDate, String endDate) {
        QueryWrapper<Nhsa3505> qw = new QueryWrapper<>();
        qw.eq("medical_code", medicalCode);
        qw.select("date(create_time)");
        qw.ge("date(create_time)", startDate);
        qw.le("date(create_time)", endDate);
        qw.groupBy("date(create_time)");
        qw.orderByDesc("date(create_time)");
        List<Object> objects = nhsa3505Mapper.selectObjs(qw);
        return objects;
    }

    /**
     * 统计指定医疗机构指定日期的全部数据记录数
     * <p>
     * 统计某个医疗机构在某个日期的所有医保3505数据记录总数，
     * 不区分是否包含追溯码信息
     * </p>
     *
     * @param medicalCode 医疗机构代码
     * @param date 统计日期，格式为YYYY-MM-DD
     * @return 该医疗机构该日期的全部数据记录数
     * @throws RuntimeException 当数据库查询失败时抛出
     */
    private Integer getNhsa3505AllDataNumber(String medicalCode, String date) {
        QueryWrapper<Nhsa3505> qw = new QueryWrapper<>();
        qw.eq("medical_code", medicalCode);
        qw.eq("date(create_time)", date);
        return Math.toIntExact(nhsa3505Mapper.selectCount(qw));
    }

    /**
     * 统计指定医疗机构指定日期包含追溯码的数据记录数
     * <p>
     * 统计某个医疗机构在某个日期的医保3505数据中，
     * 包含追溯码信息（drug_trac_info字段不为空且不为空字符串）的记录总数
     * </p>
     *
     * @param medicalCode 医疗机构代码
     * @param date 统计日期，格式为YYYY-MM-DD
     * @return 该医疗机构该日期包含追溯码的数据记录数
     * @throws RuntimeException 当数据库查询失败时抛出
     */
    private Integer getNhsa3505TraceCodeDataNumber(String medicalCode, String date) {
        QueryWrapper<Nhsa3505> qw = new QueryWrapper<>();
        qw.eq("medical_code", medicalCode);
        qw.eq("date(create_time)", date);
        qw.ne("drug_trac_info", "");
        qw.isNotNull("drug_trac_info");
        return Math.toIntExact(nhsa3505Mapper.selectCount(qw));
    }

    /**
     * 统计指定医疗机构指定日期的全部销售退货数量
     * <p>
     * 统计某个医疗机构在某个日期的所有医保3505数据中，
     * 销售退货数量（sel_retn_cnt字段）的总和，不区分是否包含追溯码
     * </p>
     *
     * @param medicalCode 医疗机构代码
     * @param date 统计日期，格式为YYYY-MM-DD
     * @return 该医疗机构该日期的全部销售退货数量总和
     * @throws RuntimeException 当数据库查询失败时抛出
     */
    private Integer getNhsa3505AllCnt(String medicalCode, String date) {
        QueryWrapper<Nhsa3505> qw = new QueryWrapper<>();
        qw.select("sum(sel_retn_cnt)");
        qw.eq("medical_code", medicalCode);
        qw.eq("date(create_time)", date);
        List<Object> objects = nhsa3505Mapper.selectObjs(qw);
        return this.getInteger(objects);
    }

    /**
     * 统计指定医疗机构指定日期包含追溯码数据的销售退货数量
     * <p>
     * 统计某个医疗机构在某个日期的医保3505数据中，
     * 包含追溯码信息的记录的销售退货数量（sel_retn_cnt字段）总和
     * </p>
     *
     * @param medicalCode 医疗机构代码
     * @param date 统计日期，格式为YYYY-MM-DD
     * @return 该医疗机构该日期包含追溯码数据的销售退货数量总和
     * @throws RuntimeException 当数据库查询失败时抛出
     */
    private Integer getNhsa3505Total(String medicalCode, String date) {
        QueryWrapper<Nhsa3505> qw = new QueryWrapper<>();
        qw.select("sum(sel_retn_cnt)");
        qw.eq("medical_code", medicalCode);
        qw.eq("date(create_time)", date);
        qw.ne("drug_trac_info", "");
        qw.isNotNull("drug_trac_info");
        List<Object> objects = nhsa3505Mapper.selectObjs(qw);
        return this.getInteger(objects);
    }

    /**
     * 统计指定医疗机构指定日期的追溯码总个数
     * <p>
     * 统计某个医疗机构在某个日期的医保3505数据中，
     * 所有追溯码的总个数。追溯码以逗号分隔存储在drug_trac_info字段中，
     * 通过计算逗号个数+1来统计追溯码数量。
     * 只统计医保同步状态为"1"且包含追溯码信息的记录
     * </p>
     *
     * @param medicalCode 医疗机构代码
     * @param date 统计日期，格式为YYYY-MM-DD
     * @return 该医疗机构该日期的追溯码总个数
     * @throws RuntimeException 当数据库查询失败时抛出
     */
    private Integer getNhsa3505TraceCodeTotal(String medicalCode, String date) {
        QueryWrapper<Nhsa3505> qw = new QueryWrapper<>();
        qw.select("SUM(CASE WHEN drug_trac_info IS NOT NULL AND TRIM( drug_trac_info ) != '' THEN LENGTH( drug_trac_info ) - LENGTH(REPLACE ( drug_trac_info, ',', '' )) + 1 ELSE 0 END )");
        qw.eq("medical_code", medicalCode);
        qw.eq("hsa_sync_status", "1");
        qw.eq("date(create_time)", date);
        qw.ne("drug_trac_info", "");
        qw.isNotNull("drug_trac_info");
        List<Object> objects = nhsa3505Mapper.selectObjs(qw);
        return this.getInteger(objects);
    }

    /**
     * 将数据库查询结果转换为Integer类型
     * <p>
     * 处理数据库聚合查询（如SUM、COUNT）的结果转换：
     * - 如果结果是BigDecimal类型，转换为Integer
     * - 如果结果为null或空列表，返回0
     * - 如果结果类型不是BigDecimal，打印错误信息并返回0
     * </p>
     *
     * @param objects 数据库查询结果列表
     * @return 转换后的Integer值，转换失败或结果为空时返回0
     */
    private Integer getInteger(List<Object> objects) {
        if (!ObjectUtils.isEmpty(objects) && objects.get(0) instanceof BigDecimal) {
            BigDecimal bigDecimalObject = (BigDecimal) objects.get(0);
            return bigDecimalObject.intValue(); // 如果您希望确保没有精度损失
        } else if (!ObjectUtils.isEmpty(objects) && objects.get(0) != null) {
            System.err.println("第一个元素不是BigDecimal类型: " + objects.get(0)
                    .getClass()
                    .getSimpleName());
            return 0; // 或者其他适当的默认值
        }
        return 0; // 或者根据上下文返回其他适当的默认值
    }
}
