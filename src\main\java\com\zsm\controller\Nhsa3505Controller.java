package com.zsm.controller;

import com.zsm.model.ApiResult;
import com.zsm.service.Nhsa3505Service;
import com.zsm.superset.TraceabilityService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;

/**
 * <p>
 * 3505销售记录报文表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-02
 */
@Tag(name = "3505销售记录", description = "3505销售记录报文表管理接口")
@RestController
@RequestMapping("/nhsa3505")
public class Nhsa3505Controller {

    @Resource
    private Nhsa3505Service nhsa3505Service;

    @Resource
    private TraceabilityService traceabilityService;

    @Operation(summary = "手动上传数据到两定接口平台", description = "手动触发数据上传到两定接口平台，支持按条件筛选上传")
    @PostMapping("/manualUpload")
    public ApiResult<String> manualUploadDataToPlatform(
            @Parameter(description = "处方明细序号，可选参数，如果传入则只上传对应的数据")
            @RequestParam(value = "cfmxxh", required = false) String cfmxxh,
            @Parameter(description = "处方序号，可选参数，如果传入则只上传对应的数据")
            @RequestParam(value = "cfxh", required = false) String cfxh) {
        return nhsa3505Service.manualUploadDataToPlatform(cfmxxh, cfxh);
    }

    @Operation(summary = "上传SuperSet统计数据", description = "手动触发上传3505数据统计信息到SuperSet平台，支持指定日期范围，不传参数默认统计今天的数据")
    @GetMapping("/uploadSuperSetData")
    public ApiResult<String> uploadSuperSetData(
            @Parameter(description = "开始日期，格式：yyyy-MM-dd，不传默认为今天")
            @RequestParam(value = "startDate", required = false) String startDate,
            @Parameter(description = "结束日期，格式：yyyy-MM-dd，不传默认为今天")
            @RequestParam(value = "endDate", required = false) String endDate) {
        return traceabilityService.generateAndUploadToSuperSet(startDate, endDate);
    }
}
