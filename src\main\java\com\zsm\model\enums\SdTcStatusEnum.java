package com.zsm.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务类型枚举
 * <AUTHOR>
 * @date 2025/5/15 下午3:54
 */
@Getter
@AllArgsConstructor
public enum SdTcStatusEnum {

    PURCHASE_IN("1001", "采购入库"),
    PURCHASE_RETURN("2001", "采购退库"),
    OUTPATIENT_DISPENSING("3001", "门诊发药"),
    OUTPATIENT_RETURN("4001", "门诊退药"),
    CANCEL_DISPENSING("5001", "取消发药"),
    CANCEL_TASK("5002", "扫码任务取消"),
    INPATIENT_DISPENSING("6001", "住院发药"),
    INPATIENT_RETURN("7001", "住院退药"),
    TRANSFER("8001", "调拨"),
    APPLY("9001", "申领");

    private final String code;
    private final String desc;
/**
     * 根据代码获取名称
     *
     * @param code 状态代码
     * @return 状态名称
     */
    public static String getNameByCode(String code) {
        for (SdTcStatusEnum statusEnum : SdTcStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum.getDesc();
            }
        }
        return "未知状态";
    }
}
