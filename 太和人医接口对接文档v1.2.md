# 太和人医系统接口对接文档

## 概述

太和人医系统是针对门诊发药业务设计的药品追溯码管理系统，提供完整的门诊、住院发药流程管理和药品追溯码采集上传功能。

**接口基础路径**: `http://************:1168/taiHeRenYi`

**认证方式**: SaaS授权验证 (部分接口需要)

---

## 1. 门诊处方相关接口(暂未实现)

### 1.1 查询门诊处方接口

**接口地址**: `POST /taiHeRenYi/queryOutpatientPrescription`

**接口描述**: 查询门诊处方信息，支持按处方号、患者ID、时间范围等条件查询

**是否需要认证**: 否

#### 请求参数 (OutpatientPrescriptionQueryDto)

```json
{
  "cardType": "查询类型（必填），1：患者ID直接查询；2：身份证号查询；3：卡号查询；4：处方号查询；5：多处方查询",
  "cfxh": "处方序号。对应his系统的唯一识别号，如：O250529003426（cardType=4和5时必填）",
  "patient_id": "患者ID，主要是用来确保内门诊和急诊药房通过患者id获取数据（cardType=1时必填）",
  "cardNo": "就诊卡号或身份证号，用于通过卡号查询患者信息后获取患者ID（cardType=2或3时必填）",
  "startTime": "开始时间，yyyy-MM-dd HH:mm:ss格式",
  "endTime": "结束时间，yyyy-MM-dd HH:mm:ss格式",
  "fg_dps": "发药单标记（0：发药；1：退药），默认0",
  "send_flag": "发送标识（0：未发送；1：已发送；2：已退药），默认1"
}
```

#### 参数说明

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| cardType | String | 是 | 查询类型，1：患者ID直接查询；2：身份证号查询；3：卡号查询；4：处方号查询 | "1" |
| cfxh | String | 条件必填 | 处方序号，多个用逗号分隔（cardType=4时必填） | "O250529003426,O250529003503" |
| patient_id | String | 条件必填 | 患者ID（cardType=1时必填） | "123456" |
| cardNo | String | 条件必填 | 就诊卡号或身份证号（cardType=2或3时必填） | "**********" |
| startTime | String | 否 | 开始时间 | "2025-06-01 00:00:00" |
| endTime | String | 否 | 结束时间 | "2025-06-01 23:59:59" |
| fg_dps | String | 否 | 发药单标记，默认"0" | "0" |
| send_flag | String | 否 | 发送标识，默认"1" | "1" |

#### 查询类型说明

根据不同的查询类型，需要提供不同的必填参数：

- **cardType=1（患者ID直接查询）**：必须提供 `patient_id` 参数
- **cardType=2（身份证号查询）**：必须提供 `cardNo` 参数（值为身份证号），系统会先通过身份证号查询患者信息获取患者ID，再查询发药数据
- **cardType=3（卡号查询）**：必须提供 `cardNo` 参数（值为就诊卡号），系统会先通过卡号查询患者信息获取患者ID，再查询发药数据
- **cardType=4（处方号查询）**：必须提供 `cfxh` 参数，直接通过处方号查询发药数据

**注意**：
- 时间范围参数 `startTime` 和 `endTime` 在所有查询类型中都是可选的，用于进一步筛选数据
- 当使用 cardType=2 或 cardType=3 时，如果系统无法找到对应的患者信息，会返回错误提示

#### 请求示例

**示例1：通过患者ID查询**
```json
{
  "cardType": "1",
  "patient_id": "123456",
  "startTime": "2025-06-01 00:00:00",
  "endTime": "2025-06-01 23:59:59"
}
```

**示例2：通过身份证号查询**
```json
{
  "cardType": "2",
  "cardNo": "110101199001011234"
}
```

**示例3：通过就诊卡号查询**
```json
{
  "cardType": "3",
  "cardNo": "**********"
}
```

**示例4：通过处方号查询**
```json
{
  "cardType": "4",
  "cfxh": "O250529003426,O250529003503"
}
```

#### 响应结果

```json
{
  "code": 200,
  "msg": "success",
  "data": [
    {
      "med_list_codg": "药品目录编码",
      "fixmedins_hilist_id": "定点医疗机构目录ID",
      "fixmedins_hilist_name": "定点医疗机构目录名称",
      "fixmedins_bchno": "定点医疗机构批次号",
      "prsc_dr_certno": "开方医师证件号码",
      "prsc_dr_name": "开方医师姓名",
      "phar_certno": "药师证件号码",
      "phar_name": "药师姓名",
      "phar_prac_cert_no": "药师执业证书编号",
      "mdtrt_sn": "就诊序列号",
      "psn_name": "人员姓名",
      "manu_lotnum": "生产批号",
      "manu_date": "生产日期",
      "expy_end": "有效期截止",
      "rx_flag": 1,
      "trdn_flag": 1,
      "rxno": "处方号",
      "rx_circ_flag": "处方流转标志",
      "rtal_docno": "零售单据号",
      "stoout_no": "库存出库单号",
      "bchno": "批次号",
      "sel_retn_cnt": "售退数量",
      "min_sel_retn_cnt": "最小售退数量",
      "selRetnUnit": "售退单位",
      "hisDosUnit": "HIS剂量单位",
      "hisPacUnit": "HIS包装单位",
      "sel_retn_time": "售退时间",
      "sel_retn_opter_name": "售退经办人姓名",
      "mdtrt_setl_type": 1,
      "spec": "规格",
      "prodentp_name": "生产企业名称",
      "cfxh": "处方序号",
      "cfmxxh": "处方明细序号",
      "sjh": "售价号",
      "patient_id": "患者ID",
      "his_con_ratio": "HIS剂量换算比例",
      "send_flag": 1,
      "send_time": "发送时间",
      "return_time": "返回时间",
      "taskIdDps": "药品追溯码任务ID",
      "taskFgStatusDps": "药品追溯码任务状态",
      "taskScanTimeDps": "药品追溯码任务创建时间"
    }
  ]
}
```

---

## 2. 住院处方相关接口

### 2.1 住院发药记录接口

**接口地址**: `POST /taiHeRenYi/queryInpatientDispenseRecord`

**接口描述**: 查询住院发药记录列表

**是否需要认证**: 否

#### 请求参数 (InpatientPrescriptionQueryDto)

```json
{
  "fyyf": "发药药房ID",
  "deptId": "发药科室ID（与fyyf意义相同，任选一个或同时提供）",
  "startTime": "开始时间，yyyy-MM-dd HH:mm:ss格式",
  "endTime": "结束时间，yyyy-MM-dd HH:mm:ss格式",
  "patWardId": "患者病区ID",
  "recordId": "发药记录ID（必填，如果提供此参数将优先使用，忽略时间范围）",
  "fgDps": "发药单标记（必填）, 0-发药, 1-退药，默认0"
}
```

#### 参数说明

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| fyyf | String | 否 | 发药药房ID | "3" |
| deptId | String | 否 | 发药科室ID（与fyyf意义相同，任选一个或同时提供） | "3" |
| startTime | String | 否 | 开始时间（与endTime配合使用，查询某个时间范围内的发药记录） | "2025-06-11 00:00:00" |
| endTime | String | 否 | 结束时间（与startTime配合使用。如果提供了recordId，则忽略时间范围） | "2025-06-11 23:59:59" |
| patWardId | String | 否 | 患者病区ID（配合筛选） | "5258" |
| recordId | String | 是 | 发药记录ID（发药单号/退药单号，如果提供此参数将优先使用，忽略时间范围） | "3532698" |
| fgDps | String | 是 | 发药单标记（0-发药；1-退药） | "0" |
```

#### 响应结果

```json
{
  "code": 200,
  "msg": "success",
  "data": [
    {
      "record_id": "发药记录ID",
      "pat_ward_id": "病区ID",
      "pat_ward_name": "病区名称",
      "fg_dps": "发药单标记（0-发药；1-退药）",
      "send_flag": "发药标志",
      "record_time": "记录时间",
      "phar_name": "药师姓名",
      "summary": "摘要信息"
    }
  ]
}
```

### 2.2 住院领药单接口

**接口地址**: `POST /taiHeRenYi/queryInpatientPrescription`

**接口描述**: 查询住院领药单详细信息

**是否需要认证**: 是

#### 请求参数 (InpatientPrescriptionQueryDto)

参数结构同2.1，但该接口必须验证SaaS授权。

#### 响应结果

```json
{
  "code": 200,
  "msg": "success",
  "data": [
    {
      "record_id": "发药记录ID",
      "record_detail_id": "发药明细ID",
      "ori_detail_id": "原发药明细ID",
      "id_fee": "费用明细ID",
      "na_fee": "费用名称",
      "sd_classify": "医嘱类别",
      "fg_dps": "发药单标记（0-发药；1-退药）",
      "send_flag": "发药标志",
      "send_time": "发药时间",
      "rtal_docno": "零售单据号",
      "stoout_no": "库存出库单号",
      "pat_ward_id": "患者病区id",
      "pat_ward_name": "患者病区名称",
      "fyyf": "发药药房id",
      "dept_id": "发药科室ID",
      "phar_certno": "药师证件号码",
      "phar_name": "药师姓名",
      "phar_prac_cert_no": "药师执业证书编号",
      "sel_retn_time": "售退时间",
      "his_drug_code": "HIS药品编码",
      "med_list_codg": "医疗目录编码",
      "spec": "规格",
      "prodentp_name": "生产企业名称",
      "fixmedins_hilist_id": "定点医药机构目录编号",
      "fixmedins_hilist_name": "定点医药机构目录名称",
      "manu_lotnum": "生产批号",
      "manu_date": "生产日期",
      "expy_end": "有效期止",
      "bchno": "批次号",
      "rx_flag": "处方药标志",
      "trdn_flag": "拆零标志",
      "his_dos_unit": "HIS的剂量单位",
      "his_pac_unit": "HIS的包装单位",
      "his_con_ratio": "转换比",
      "fixmedins_bchno": "定点医药机构批次流水号",
      "pat_in_hos_id": "住院ID",
      "mdtrt_sn": "就医流水号",
      "psn_name": "人员姓名",
      "bed_no": "床位号",
      "mdtrt_setl_type": "就诊结算类型",
      "order_id": "医嘱id",
      "prsc_dr_certno": "开方医师证件号码",
      "prsc_dr_name": "开方医师姓名",
      "sel_retn_cnt": "售退数量",
      "min_sel_retn_cnt": "最小售退数量",
      "sel_retn_unit": "售退单位",
      "sel_retn_opter_name": "售退经办人姓名"
    }
  ]
}
```

---

## 3. 药品追溯码相关接口

### 3.1 药品追溯码扫描与上传接口

**接口地址**: `POST /taiHeRenYi/uploadScans`

**接口描述**: 将本次采集的所有药品追溯码信息批量提交

**是否需要认证**: 是

#### 请求参数 (TraceabilityUploadDto)

```json
{
  "fgDps": "退药单标记;0: 发药, 1: 退药",
  "sdDps": "发药单类型;1: 住院处方, 2:门诊处方",
  "patWardId": "患者病区ID",
  "patWardName": "患者病区名称",
  "window": "窗口",
  "selRetnOpterId": "选择退药操作员ID",
  "prescriptions": [
    {
      "sendTime": "发送时间",
      "outPresId": "处方ID",
      "presCode": "处方编号",
      "patId": "患者ID",
      "patName": "患者姓名",
      "cardNo": "就诊卡号",
      "idDept": "发药科室id",
      "drugItems": [
        {
          "outPresdetailid": "处方明细ID",
          "drugCode": "药品编码",
          "drugName": "药品名称",
          "spec": "药品规格",
          "prodentpName": "生产企业",
          "quantity": 10,
          "unit": "药品单位",
          "price": 25.50,
          "amount": 255.00,
          "drugtracinfoScanned": "扫描的追溯码",
          "minDoseCount": 1,
          "minPackingName": "最小包装名称",
          "trdnFlag": 0,
          "dispCnt": 10,
          "cfxh": "处方号",
          "cfmxxh": "处方明细号"
        }
      ]
    }
  ]
}
```

#### 参数说明

**主要参数**

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| fgDps | String | 是 | 退药单标记;0: 发药, 1: 退药 | "0" |
| sdDps | String | 否 | 发药单类型;1: 住院处方, 2:门诊处方 | "1" |
| patWardId | String | 否 | 患者病区ID（最大长度100字符） | "5258" |
| patWardName | String | 否 | 患者病区名称（最大长度100字符） | "内科一病区" |
| window | String | 否 | 窗口 | "1" |
| selRetnOpterId | String | 否 | 选择退药操作员ID | "OP001" |
| prescriptions | Array | 是 | 处方项目明细列表 | - |

**PrescriptionItem 参数**

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| sendTime | String | 是 | 发送时间 | "2025-06-01 10:00:00" |
| outPresId | String | 是 | 处方ID | "P123456" |
| presCode | String | 否 | 处方编号 | "PC123456" |
| patId | String | 是 | 患者ID | "PAT001" |
| patName | String | 是 | 患者姓名 | "张三" |
| cardNo | String | 否 | 就诊卡号 | "CARD001" |
| idDept | String | 是 | 发药科室id | "DEPT001" |
| drugItems | Array | 是 | 药品明细列表 | - |

**DrugItem 参数**

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| outPresdetailid | String | 是 | 处方明细ID | "PD123456" |
| drugCode | String | 是 | 药品编码 | "D001" |
| drugName | String | 是 | 药品名称 | "阿莫西林胶囊" |
| spec | String | 否 | 药品规格 | "0.25g×24粒" |
| prodentpName | String | 否 | 生产企业 | "某某制药有限公司" |
| quantity | Integer | 是 | 药品数量 | 10 |
| unit | String | 是 | 药品单位 | "盒" |
| price | Double | 是 | 药品单价 | 25.50 |
| amount | Double | 是 | 药品总价 | 255.00 |
| drugtracinfoScanned | String | 否 | 扫描的追溯码 | "12345678901234567890" |
| minDoseCount | Integer | 否 | 最小制剂单位数量 | 1 |
| minPackingName | String | 否 | 最小包装名称 | "粒" |
| trdnFlag | Integer | 否 | 拆零标识:0未拆零,1拆零 | 0 |
| dispCnt | Integer | 否 | 发药数量 | 10 |
| cfxh | String | 否 | 处方号 | "CF123456" |
| cfmxxh | String | 否 | 处方明细号 | "CFM001" |
```

#### 响应结果

```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "success": ["成功的处方ID列表"],
    "fail": ["失败的处方ID列表"],
    "failMessages": {
      "处方ID": "错误信息"
    }
  }
}
```

### 3.2 取消扫码任务

**接口地址**: `POST /taiHeRenYi/cancel/{taskId}`

**接口描述**: 根据任务ID取消未完成的扫码任务

**是否需要认证**: 是

#### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| taskId | Long | 是 | 任务ID |

#### 响应结果

```json
{
  "code": 200,
  "msg": "success",
  "data": "取消成功"
}
```

### 3.3 单个追溯码及其生命周期查询

**接口地址**: `GET /taiHeRenYi/YsfStoTc/getByDrugtracinfo/{drugtracinfo}`

**接口描述**: 根据追溯码查询其基本信息和状态变更历史

**是否需要认证**: 是

#### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| drugtracinfo | String | 是 | 追溯码 |

#### 响应结果

```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "traceabilityCodeInfo": {
      "追溯码基本信息": "YsfStoTc实体对象"
    },
    "hisDrugDict": {
      "药品字典信息": "HisDrugDict实体对象"
    },
    "statusHistory": [
      {
        "状态历史记录": "YsfStoTcStatus实体对象列表"
      }
    ],
    "medListCodg": "医疗目录编码",
    "drugName": "药品名称",
    "spec": "药品规格",
    "prodentpName": "生产企业名称"
  }
}
```

---

## 4. 发药单管理接口

### 4.1 查询发药单列表

**接口地址**: `GET /taiHeRenYi/YsfStoDps/list`

**接口描述**: 根据条件查询发药单列表信息，包含分页

**是否需要认证**: 是

#### 请求参数 (DispensingRecordQueryDto)

```json
{
  "patientName": "患者姓名",
  "prescriptionId": "处方号 (cfxh 或 outPresId)",
  "dispensingStatus": "发药单状态 (fg_status)",
  "deptId": "发药科室ID",
  "startTime": "发药开始时间，yyyy-MM-dd HH:mm:ss格式",
  "endTime": "发药结束时间，yyyy-MM-dd HH:mm:ss格式",
  "fgDps": "退药单标记;0: 发药, 1: 退药",
  "sdDps": "发药单类型;1: 住院处方, 2:门诊处方",
  "patWardId": "患者病区ID",
  "pageNum": "页码，默认1",
  "pageSize": "每页数量，默认10"
}
```

#### 参数说明

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| patientName | String | 否 | 患者姓名 | "张三" |
| prescriptionId | String | 否 | 处方号 (cfxh 或 outPresId) | "P123456" |
| dispensingStatus | String | 否 | 发药单状态 (fg_status) | "1" |
| deptId | String | 否 | 发药科室ID | "DEPT001" |
| startTime | Date | 否 | 发药开始时间 | "2025-06-01 00:00:00" |
| endTime | Date | 否 | 发药结束时间 | "2025-06-01 23:59:59" |
| fgDps | String | 否 | 退药单标记;0: 发药, 1: 退药 | "0" |
| sdDps | String | 否 | 发药单类型;1: 住院处方, 2:门诊处方 | "1" |
| patWardId | String | 否 | 患者病区ID | "5258" |
| pageNum | Integer | 否 | 页码，默认1 | 1 |
| pageSize | Integer | 否 | 每页数量，默认10 | 10 |

#### 响应结果

```json
{
  "code": 200,
  "msg": "success",
  "total": 100,
  "rows": [
    {
      "发药单记录信息": "DispensingRecordVo对象"
    }
  ]
}
```

### 4.2 查询发药单明细列表

**接口地址**: `GET /taiHeRenYi/YsfStoDpsSub/list`

**接口描述**: 根据发药单ID查询发药单明细列表，支持按药品名称/编码、是否已采集等条件过滤

**是否需要认证**: 是

#### 请求参数 (DispensingRecordDetailQueryDto)

具体参数结构请参考DispensingRecordDetailQueryDto类定义。

#### 响应结果

```json
{
  "code": 200,
  "msg": "success",
  "total": 50,
  "rows": [
    {
      "id": 1,
      "idDps": "发药单ID",
      "cfxh": "处方号",
      "cfmxxh": "处方明细序号",
      "drugCode": "药品编码",
      "naFee": "药品名称",
      "priceSale": 25.50,
      "selRetnCnt": 10,
      "amtTotal": 255.00,
      "unitSale": "盒",
      "unitSaleFactor": 1,
      "drugtracinfo": "追溯码",
      "idSub": 1001,
      "fgScanned": "是否已扫码 (0=未扫码, 1=已扫码)",
      "createTime": "2025-06-01 10:00:00",
      "updateTime": "2025-06-01 10:00:00",
      "collectedCount": 5
    }
  ]
}
```

---

## 5. 扫码任务管理接口

### 5.1 查询扫码任务列表

**接口地址**: `GET /taiHeRenYi/YsfStoTcTask/list`

**接口描述**: 支持按任务状态、患者姓名、业务单号、任务类型、创建时间范围等查询

**是否需要认证**: 是

#### 请求参数 (YsfStoTcTaskQueryDto)

```json
{
  "taskStatus": "任务状态 (fg_status)",
  "remark": "患者姓名",
  "bizCode": "业务单号 (cd_biz 即 outPresId)",
  "taskType": "任务类型 (sd_task_type)",
  "startTime": "创建开始时间",
  "endTime": "创建结束时间",
  "pageNum": 1,
  "pageSize": 10
}
```

#### 响应结果

```json
{
  "code": 200,
  "msg": "success",
  "total": 30,
  "rows": [
    {
      "扫码任务信息": "YsfStoTcTask对象"
    }
  ]
}
```

### 5.2 查询扫码任务明细列表

**接口地址**: `GET /taiHeRenYi/YsfStoTcTaskSub/list`

**接口描述**: 根据任务ID查询扫码任务明细列表，支持按药品名称/编码、是否已扫码等条件过滤

**是否需要认证**: 是

#### 请求参数 (YsfStoTcTaskSubQueryDto)

具体参数结构请参考YsfStoTcTaskSubQueryDto类定义。

#### 响应结果

```json
{
  "code": 200,
  "msg": "success",
  "total": 20,
  "rows": [
    {
      "扫码任务明细信息": "YsfStoTcTaskSub对象"
    }
  ]
}
```

---

## 6. 追溯码流转记录接口

### 6.1 查询追溯码流转记录列表

**接口地址**: `GET /taiHeRenYi/YsfStoTcStatus/list`

**接口描述**: 根据条件查询追溯码流转记录列表

**是否需要认证**: 是

#### 请求参数 (YsfStoTcStatusQueryDto)

具体参数结构请参考YsfStoTcStatusQueryDto类定义。

#### 响应结果

```json
{
  "code": 200,
  "msg": "success",
  "total": 100,
  "rows": [
    {
      "流转记录信息": "YsfStoTcStatus对象"
    }
  ]
}
```

---

## 7. 其他接口

### 7.1 根据业务子ID查询发药明细

**接口地址**: `GET /taiHeRenYi/dispensing/detailsByBizSubId`

**接口描述**: 通过业务子ID（如发药单明细ID）直接查询关联的发药信息

**是否需要认证**: 是

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| bizSubId | String | 是 | 业务子ID，通常是发药单明细ID或处方明细ID |

#### 响应结果

```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "bizSubId": "业务子ID",
    "dispensingId": "发药单ID",
    "dispensingNo": "发药单号",
    "prescriptionNo": "处方号",
    "patientId": "患者ID",
    "patientName": "患者姓名",
    "drugCode": "商品编码",
    "drugName": "商品名称",
    "specification": "商品规格",
    "unit": "单位",
    "quantity": 10.00,
    "price": 25.50,
    "amount": 255.00,
    "dispensingTime": "2025-06-01 10:00:00",
    "dispensingUser": "发药人",
    "storageId": "库房ID",
    "storageName": "库房名称",
    "tracedCount": 5,
    "traceableCodes": ["追溯码列表"]
  }
}
```

### 7.2 退药上传

**接口地址**: `POST /taiHeRenYi/return`

**接口描述**: 上传退药信息进行退药处理

**是否需要认证**: 是

#### 请求参数 (TraceabilityUploadDto)

参数结构同3.1药品追溯码扫描与上传接口，但需要设置fgDps为"1"表示退药。

#### 响应结果

```json
{
  "code": 200,
  "msg": "success",
  "data": "退药处理成功"
}
```

---

## 8. 定时任务说明

太和人医系统包含以下几个重要的定时任务，用于自动处理发药时间同步和数据上传：

### 8.1 同步发药时间任务

**任务名称**: `syncDispenseTimeTask`

**执行频率**: 每5分钟执行一次 (`0 0/5 * * * ?`)

**功能描述**: 
- 自动同步太和人医系统的发药时间
- 确保发药记录的时间准确性
- 调用 `nhsa3505Service.syncTaiHeDispenseTime()` 方法实现同步逻辑

**业务流程**:
1. 系统定时检查未同步发药时间的记录
2. 从HIS系统获取最新的发药时间信息
3. 更新本地数据库中的发药时间字段
4. 记录同步日志，包括成功和失败的情况

### 8.2 上传3505数据到最终平台任务

**任务名称**: `uploadDataToPlatformTask`

**执行频率**: 每3小时执行一次 (`0 0 */3 * * ?`)

**功能描述**:
- 自动将3505销售记录数据上传到医保平台
- 确保药品销售数据及时上报合规
- 调用 `nhsa3505Service.uploadDataToPlatform()` 方法实现上传逻辑

**业务流程**:
1. 查询待上传的3505销售记录
2. 将数据按照医保平台要求的格式进行封装
3. 调用医保平台接口进行数据上传
4. 更新数据的上传状态和时间
5. 记录上传结果日志

### 8.3 生成追溯码数据并上传SuperSet平台任务

**任务名称**: `uploadTraceabilityDataToSuperSetTask`

**执行频率**: 每2小时执行一次 (`0 0 */2 * * ?`)

**功能描述**:
- 自动生成药品追溯码相关的统计数据
- 将数据上传到SuperSet数据分析平台
- 支持数据可视化和业务分析
- 调用 `traceabilityService.generateAndUploadToSuperSet()` 方法实现

**业务流程**:
1. 从追溯码相关表中提取统计数据
2. 按照SuperSet平台的数据格式要求进行数据转换
3. 生成包含发药量、追溯码覆盖率等关键指标的数据集
4. 调用SuperSet平台API上传数据
5. 更新数据生成和上传的时间戳

### 8.4 定时任务监控和异常处理

**日志记录**:
- 所有定时任务都有完整的日志记录
- 包括任务开始时间、执行结果、异常信息等
- 使用slf4j进行日志输出

**异常处理**:
- 所有定时任务都有异常捕获机制
- 异常发生时会记录详细的错误信息
- 不会因为单次执行失败影响后续任务执行

**性能考虑**:
- 任务执行时间间隔设置合理，避免系统负载过高
- 数据处理采用分批处理方式，防止大量数据处理超时
- 对于长时间运行的任务，考虑添加进度监控

---

## 9. 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

---

## 10. 注意事项

1. **认证要求**: 标注需要SaaS授权验证的接口必须在请求头中包含有效的授权信息
2. **时间格式**: 所有时间字段统一使用 `yyyy-MM-dd HH:mm:ss` 格式
3. **分页查询**: 列表查询接口支持分页，通过pageNum和pageSize参数控制
4. **数据完整性**: 涉及药品追溯码的接口要确保追溯码的唯一性和完整性
5. **异常处理**: 所有接口都有统一的异常处理机制，返回标准的错误格式
6. **定时任务**: 定时任务的执行频率可根据实际业务需求进行调整
7. **数据同步**: 注意HIS系统与追溯码系统之间的数据同步时效性

---

**文档版本**: v1.0  
**更新时间**: 2025-06-11  
**维护人员**: 周士钰 