package com.zsm.service;

import com.zsm.model.enums.SyncAccountEnum;
import com.zsm.model.vo.InPatientDispenseDetailBindScatteredVo;

import java.util.List;

/**
 * 数据持久化服务接口
 * 提供通用的数据保存功能，避免Service层循环依赖
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface DataPersistenceService {

    /**
     * 保存住院发药数据到相关表
     * 包括发药单主表、明细表和追溯码相关表
     *
     * @param batch           批次数据
     * @param account         同步账号枚举
     * @param ymfUserId       云码付用户ID
     * @param includeNhsa3505 是否包含3505表数据保存
     */
    void saveInpatientDispenseData(List<InPatientDispenseDetailBindScatteredVo> batch, SyncAccountEnum account, Long ymfUserId, boolean includeNhsa3505);

    /**
     * 保存住院发药数据到相关表
     * 包括发药单主表、明细表和追溯码相关表（默认包含3505表保存）
     *
     * @param batch     批次数据
     * @param account   同步账号枚举
     * @param ymfUserId 云码付用户ID
     */
    default void saveInpatientDispenseData(List<InPatientDispenseDetailBindScatteredVo> batch, SyncAccountEnum account, Long ymfUserId) {
        saveInpatientDispenseData(batch, account, ymfUserId, true);
    }

    /**
     * 保存住院发药数据到相关表（简化版本，不需要账号信息）
     * 使用默认账号信息，默认包含3505表保存
     *
     * @param batch 批次数据
     */
    default void saveInpatientDispenseData(List<InPatientDispenseDetailBindScatteredVo> batch) {
        SyncAccountEnum defaultAccount = SyncAccountEnum.TAIHE_IV_CENTER;
        Long defaultYmfUserId = 1L;
        saveInpatientDispenseData(batch, defaultAccount, defaultYmfUserId, true);
    }

    /**
     * 仅保存发药相关数据，不包含3505表
     * 适用于3505数据已经通过其他方式保存的场景
     *
     * @param batch     批次数据
     * @param account   同步账号枚举
     * @param ymfUserId 云码付用户ID
     */
    default void saveDispenseDataOnly(List<InPatientDispenseDetailBindScatteredVo> batch, SyncAccountEnum account, Long ymfUserId) {
        saveInpatientDispenseData(batch, account, ymfUserId, false);
    }

    /**
     * 仅保存发药相关数据，不包含3505表（使用用户名密码）
     * 适用于3505数据已经通过其他方式保存的场景，使用字符串形式的账号信息
     *
     * @param batch     批次数据
     * @param username  用户名
     * @param password  密码
     * @param ymfUserId 云码付用户ID
     */
    void saveDispenseDataOnlyByCredentials(List<InPatientDispenseDetailBindScatteredVo> batch, String username, String password, Long ymfUserId);
}