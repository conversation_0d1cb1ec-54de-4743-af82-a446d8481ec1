package com.zsm.model.nhsa.request.fsi3506;


import com.zsm.model.nhsa.request.DrugTracInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 3506退货入参报文
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Selinfo3506 {

    /**
     * 医疗目录编码
     */
    private String med_list_codg;
    /**
     * 定点医药机构目录编号
     */
    private String fixmedins_hilist_id;
    /**
     * 定点医药机构目录名称
     */
    private String fixmedins_hilist_name;
    /**
     * 定点医药机构批次流水号
     */
    private String fixmedins_bchno;
    /**
     * 结算ID
     */
    private String setl_id;
    /**
     * 人员编号
     */
    private String psn_no;
    /**
     * 人员证件类型
     */
    private String psn_cert_type;
    /**
     * 证件号码
     */
    private String certno;
    /**
     * 人员姓名
     */
    private String psn_name;
    /**
     * 生产批号
     */
    private String manu_lotnum;

    /**
     * 生产日期
     */
    private Date manu_date;

    /**
     * 有效期止
     */
    private Date expy_end;

    /**
     * 处方药标志
     */
    private String rx_flag;
    /**
     * 拆零标志
     */
    private String trdn_flag;
    /**
     * 最终成交单价
     */
    private BigDecimal finl_trns_pric;
    /**
     * 销售/退货数量
     */
    private BigDecimal sel_retn_cnt;

    /**
     * 销售/退货时间
     */
    private Date sel_retn_time;
    /**
     * 销售/退货经办人姓名
     */
    private String sel_retn_opter_name;
    /**
     * 备注
     */
    private String memo;
    /**
     * 商品销售流水号
     */
    private String medins_prod_sel_no;
    /**
     * 就医流水号
     */
    private String mdtrt_sn;

    /**
     * 溯源码节点信息
     */
    private List<DrugTracInfo> drugtracinfo;

}