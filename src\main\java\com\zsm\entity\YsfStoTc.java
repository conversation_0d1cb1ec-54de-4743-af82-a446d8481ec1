package com.zsm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 商品追溯码实体类
 * 
 * <AUTHOR>
 * @since 2025-06-02
 */
@Data
@TableName("ysf_sto_tc")
@Schema(description = "商品追溯码")
public class YsfStoTc implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @Schema(description = "主键")
    @TableId(value = "id_tc", type = IdType.AUTO)
    private Long idTc;

    /**
     * 追溯码类型(废弃);用于标识是按明细的还是按商品的,按明细的会对记录进行核销
     */
    @Schema(description = "追溯码类型(废弃);用于标识是按明细的还是按商品的,按明细的会对记录进行核销")
    @Size(max = 100, message = "追溯码类型(废弃);用于标识是按明细的还是按商品的,按明细的会对记录进行核销长度不能超过100个字符")
    @TableField("sd_tc")
    private String sdTc;

    /**
     * 追溯码管理模式;数据落地时的追溯码管理模式,简易管理/严格管理
     */
    @Schema(description = "追溯码管理模式;数据落地时的追溯码管理模式,简易管理/严格管理")
    @Size(max = 100, message = "追溯码管理模式;数据落地时的追溯码管理模式,简易管理/严格管理长度不能超过100个字符")
    @TableField("sd_tc_manage")
    private String sdTcManage;

    /**
     * 商品编码
     */
    @Schema(description = "商品编码")
    @Size(max = 100, message = "商品编码长度不能超过100个字符")
    @TableField("drug_code")
    private String drugCode;

    /**
     * 库存id;预留字段,后期扩展用
     */
    @Schema(description = "库存id;预留字段,后期扩展用")
    @Size(max = 100, message = "库存id;预留字段,后期扩展用长度不能超过100个字符")
    @TableField("id_sto_inv")
    private String idStoInv;

    /**
     * 部门id;预留字段,后期扩展用
     */
    @Schema(description = "部门id;预留字段,后期扩展用")
    @Size(max = 100, message = "部门id;预留字段,后期扩展用长度不能超过100个字符")
    @TableField("id_dept")
    private String idDept;

    /**
     * 追溯码
     */
    @Schema(description = "追溯码")
    @Size(max = 100, message = "追溯码长度不能超过100个字符")
    @TableField("drugtracinfo")
    private String drugtracinfo;

    /**
     * 追溯码包装转换系数
     */
    @Schema(description = "追溯码包装转换系数")
    @TableField("unit_sale_factor")
    private Integer unitSaleFactor;

    /**
     * 追溯码对应的包装
     */
    @Schema(description = "追溯码对应的包装")
    @Size(max = 100, message = "追溯码对应的包装长度不能超过100个字符")
    @TableField("unit_tc")
    private String unitTc;

    /**
     * 剩余数量;用于拆零卖的情况,剩余数量<=包装转换系数
     */
    @Schema(description = "剩余数量;用于拆零卖的情况,剩余数量<=包装转换系数")
    @TableField("amount_rem")
    private BigDecimal amountRem;

    /**
     * 有效标志
     */
    @Schema(description = "有效标志")
    @Size(max = 100, message = "有效标志长度不能超过100个字符")
    @TableField("fg_active")
    private String fgActive;

    /**
     * 机构编号
     */
    @Schema(description = "机构编号")
    @Size(max = 100, message = "机构编号长度不能超过100个字符")
    @TableField("id_org")
    private String idOrg;

    /**
     * 生产批号
     */
    @Schema(description = "生产批号")
    @Size(max = 100, message = "生产批号长度不能超过100个字符")
    @TableField("manu_lotnum")
    private String manuLotnum;

    /**
     * 生产日期
     */
    @Schema(description = "生产日期")
    @TableField("manu_date")
    private LocalDateTime manuDate;

    /**
     * 有效期止
     */
    @Schema(description = "有效期止")
    @TableField("expy_end")
    private LocalDateTime expyEnd;

    /**
     * 医疗机构编码
     */
    @Schema(description = "医疗机构编码")
    @Size(max = 100, message = "医疗机构编码长度不能超过100个字符")
    @TableField("org_id")
    private String orgId;

    /**
     * 医疗机构名称
     */
    @Schema(description = "医疗机构名称")
    @NotBlank(message = "医疗机构名称不能为空")
    @Size(max = 30, message = "医疗机构名称长度不能超过30个字符")
    @TableField("org_name")
    private String orgName;

    /**
     * 乐观锁
     */
    @Schema(description = "乐观锁")
    @Size(max = 100, message = "乐观锁长度不能超过100个字符")
    @TableField("revision")
    private String revision;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    @Size(max = 100, message = "创建者长度不能超过100个字符")
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    @Size(max = 100, message = "更新者长度不能超过100个字符")
    @TableField("update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @TableField("remark")
    private String remark;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @Schema(description = "删除标志（0代表存在 1代表删除）")
    @Size(max = 100, message = "删除标志（0代表存在 1代表删除）长度不能超过100个字符")
    @TableField("del_flag")
    private String delFlag;
} 
