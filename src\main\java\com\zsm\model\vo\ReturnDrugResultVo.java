package com.zsm.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 退药结果响应类
 * <AUTHOR>
 * @date 2025/7/10 
 */
@Data
@Schema(description = "退药结果响应类")
public class ReturnDrugResultVo implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 退药成功的处方ID列表
     */
    @Schema(description = "退药成功的处方ID列表")
    private List<String> success;
    
    /**
     * 退药失败的处方ID列表
     */
    @Schema(description = "退药失败的处方ID列表")
    private List<String> fail;
    
    /**
     * 失败处方的错误信息，key为处方ID，value为错误信息
     */
    @Schema(description = "失败处方的错误信息，key为处方ID，value为错误信息")
    private Map<String, String> failMessages;
    
    /**
     * 成功处方的详细信息，key为处方ID，value为处理详情
     */
    @Schema(description = "成功处方的详细信息，key为处方ID，value为处理详情")
    private Map<String, String> successDetails;
    
    /**
     * 统计信息
     */
    @Schema(description = "统计信息")
    private StatisticsInfo statistics;
    
    /**
     * 统计信息内部类
     */
    @Data
    @Schema(description = "统计信息")
    public static class StatisticsInfo implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 总处方数量
         */
        @Schema(description = "总处方数量")
        private Integer totalCount;
        
        /**
         * 成功处方数量
         */
        @Schema(description = "成功处方数量")
        private Integer successCount;
        
        /**
         * 失败处方数量
         */
        @Schema(description = "失败处方数量")
        private Integer failCount;
        
        /**
         * 总体状态：SUCCESS-全部成功，FAIL-全部失败，PARTIAL-部分成功
         */
        @Schema(description = "总体状态：SUCCESS-全部成功，FAIL-全部失败，PARTIAL-部分成功")
        private String overallStatus;
    }
}