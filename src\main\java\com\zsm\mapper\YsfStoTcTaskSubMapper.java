package com.zsm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zsm.entity.YsfStoTcTaskSub;
import com.zsm.model.dto.YsfStoTcTaskSubQueryDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 扫码任务明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-02
 */
@Mapper
public interface YsfStoTcTaskSubMapper extends BaseMapper<YsfStoTcTaskSub> {

    /**
     * 根据条件查询扫码任务明细分页列表
     *
     * @param page 分页参数
     * @param queryDto 查询条件
     * @return 任务明细分页列表
     */
    IPage<YsfStoTcTaskSub> selectTaskSubList(IPage<YsfStoTcTaskSub> page, @Param("queryDto") YsfStoTcTaskSubQueryDto queryDto);
}
