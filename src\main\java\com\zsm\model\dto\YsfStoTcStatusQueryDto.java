package com.zsm.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.util.Date;

/**
 * 追溯码流转记录查询DTO
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@Schema(description = "追溯码流转记录查询DTO")
public class YsfStoTcStatusQueryDto {

    /**
     * 追溯码
     */
    @NotNull(message = "追溯码不能为空")
    @Schema(description = "追溯码")
    private String drugtracinfo;

    /**
     * 业务类型
     */
    @Schema(description = "业务类型")
    private String sdTcStatus;

    /**
     * 库房ID
     */
    @Schema(description = "库房ID", example = "库房1")
    private String idSto;

    /**
     * 商品编码
     */
    @Schema(description = "商品编码", example = "商品1")
    private String drugCode;

    /**
     * 原始业务主键
     */
    @Schema(description = "原始业务主键", example = "主键1")
    private String idBizOri;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间", example = "2025-06-10")
    private Date beginTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间", example = "2025-06-10")
    private Date endTime;

    /**
     * 页码
     */
    @NotNull(message = "页码不能为空")
    @Schema(description = "页码", example = "1")
    private Integer pageNum;

    /**
     * 每页数量
     */
    @NotNull(message = "每页数量不能为空")
    @Schema(description = "每页数量", example = "10")
    private Integer pageSize;
} 