package com.zsm.common.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * SaaS HTTP请求异常类
 * 用于封装HTTP请求过程中的异常信息
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SaasHttpException extends RuntimeException {
    
    private final String endpoint;
    private final String requestData;
    private final String responseData;
    private final Integer httpStatus;
    
    public SaasHttpException(String message) {
        super(message);
        this.endpoint = null;
        this.requestData = null;
        this.responseData = null;
        this.httpStatus = null;
    }
    
    public SaasHttpException(String message, Throwable cause) {
        super(message, cause);
        this.endpoint = null;
        this.requestData = null;
        this.responseData = null;
        this.httpStatus = null;
    }
    
    public SaasHttpException(String message, String endpoint, String requestData, String responseData, Integer httpStatus) {
        super(message);
        this.endpoint = endpoint;
        this.requestData = requestData;
        this.responseData = responseData;
        this.httpStatus = httpStatus;
    }
    
    public SaasHttpException(String message, String endpoint, String requestData, String responseData, Integer httpStatus, Throwable cause) {
        super(message, cause);
        this.endpoint = endpoint;
        this.requestData = requestData;
        this.responseData = responseData;
        this.httpStatus = httpStatus;
    }
} 