package com.zsm.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsm.entity.YsfStoTcTask;
import com.zsm.mapper.YsfStoTcTaskMapper;
import com.zsm.model.TableDataInfo;
import com.zsm.model.dto.YsfStoTcTaskQueryDto;
import com.zsm.service.YsfStoTcTaskService;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * <p>
 * 扫码任务记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-02
 */
@Service
public class YsfStoTcTaskServiceImpl extends ServiceImpl<YsfStoTcTaskMapper, YsfStoTcTask> implements YsfStoTcTaskService {
    @Resource
    private YsfStoTcTaskMapper ysfStoTcTaskMapper;

    /**
     * 查询扫码任务列表
     *
     * @param queryDto 查询条件
     * @return 扫码任务分页数据
     */
    @Override
    public TableDataInfo queryTaskList(YsfStoTcTaskQueryDto queryDto) {
        try {
            // 设置分页参数
            int pageNum = queryDto.getPageNum() == null ? 1 : queryDto.getPageNum();
            int pageSize = queryDto.getPageSize() == null ? 10 : queryDto.getPageSize();
            Page<YsfStoTcTask> page = new Page<>(pageNum, pageSize);
            
            // 使用MyBatis-Plus分页查询
            IPage<YsfStoTcTask> pageResult = ysfStoTcTaskMapper.selectTaskList(page, queryDto);

            // 封装结果
            TableDataInfo tableDataInfo = new TableDataInfo();
            tableDataInfo.setCode(200);
            tableDataInfo.setMsg("查询成功");
            tableDataInfo.setRows(pageResult.getRecords());
            tableDataInfo.setTotal(pageResult.getTotal());

            return tableDataInfo;
        } catch (Exception e) {
            // 记录异常信息
            e.printStackTrace();
            // 返回空结果
            TableDataInfo tableDataInfo = new TableDataInfo();
            tableDataInfo.setCode(500);
            tableDataInfo.setMsg("查询失败：" + e.getMessage());
            tableDataInfo.setRows(null);
            tableDataInfo.setTotal(0);
            return tableDataInfo;
        }
    }
}
