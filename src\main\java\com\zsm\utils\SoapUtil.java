package com.zsm.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zsm.common.exception.BusinessErrorEnum;
import com.zsm.common.exception.BusinessException;
import com.zsm.model.ApiResult;
import com.zsm.model.vo.InpatientPrescriptionResponseVo;
import com.zsm.model.vo.OutpatientPrescriptionResponseVo;
import com.zsm.model.vo.SoapResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 东华SOAP工具类
 * 提供SOAP Web Service调用的高级功能
 */
@Slf4j
public class SoapUtil {

    /**
     * 私有构造函数，防止实例化
     */
    private SoapUtil() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    private static final String WSDL_URL = "https://**************:1443/csp/hsb/DHC.Published.PUB0026.BS.PUB0026.CLS";
    
    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 发送SOAP请求并解析响应（通用方法）
     *
     * @param input1 输入参数1
     * @param input2 输入参数2（可以是字符串或对象）
     * @return 解析后的响应数据
     */
    public static SoapResponseVo callSoapServiceWithParams(String input1, Object input2) {

        try {
            // 将input2转换为JSON字符串
            String input2Json;
            if (input2 instanceof String) {
                input2Json = (String) input2;
            } else {
                input2Json = new JSONObject(input2).toString();
            }
            log.info("开始调用SOAP接口: {}", WSDL_URL);
            log.info("请求参数 - input1: {}, input2: {}", input1, input2Json);

            // 构建SOAP请求XML
            String soapRequestXml = buildSoapRequestXml(input1, input2Json);
            log.info("SOAP请求XML: {}", soapRequestXml);
            
            // 发送SOAP请求
            HttpResponse response = HttpRequest.post(WSDL_URL)
                    .header("Content-Type", "text/xml; charset=utf-8")
                    .header("SOAPAction", "\"\"")
                    .body(soapRequestXml)
                    .timeout(30000) // 30秒超时
                    .execute();
            
            log.info("SOAP响应状态码: {}", response.getStatus());

            if (!response.isOk()) {
                // HTTP状态码异常，抛出第三方API异常
                throw new BusinessException(BusinessErrorEnum.SOAP_SERVICE_UNAVAILABLE.getCode(),
                        String.format("SOAP接口调用失败，状态码: %d, 响应: %s", response.getStatus(), response.body()));
            }

            String responseBody = response.body();
            log.info("SOAP响应内容: {}", responseBody);
            
            // 解析SOAP响应
            SoapResponseVo responseData = parseSoapResponse(responseBody);
            
            // 检查SOAP故障
            checkSoapFault(responseData);
            
            log.info("SOAP接口调用成功");
            return responseData;
            
        } catch (BusinessException e) {
            // 重新抛出业务异常，由全局异常处理器处理
            throw e;
        } catch (Exception e) {
            // 其他异常包装为业务异常
            log.error("SOAP接口调用异常: {}", e.getMessage(), e);
            throw new BusinessException(BusinessErrorEnum.SOAP_SERVICE_UNAVAILABLE, e);
        }
    }

    /**
     * 检查SOAP故障
     */
    private static void checkSoapFault(SoapResponseVo responseData) {
        if (responseData.hasSoapFault()) {
            log.error("SOAP故障 - 错误码: {}, 错误信息: {}", responseData.getFaultCode(), responseData.getFaultString());
            throw new BusinessException(BusinessErrorEnum.SOAP_FAULT_ERROR.getCode(),
                    String.format("SOAP故障: %s - %s", responseData.getFaultCode(), responseData.getFaultString()));
        }
    }

    /**
     * 检查业务状态码（公开方法，供业务层按需调用）
     *
     * @param responseData SOAP响应数据
     */
    public static void checkBusinessCode(SoapResponseVo responseData) {
        JSONObject parsedResult = responseData.getParsedResult();
        if (parsedResult != null) {
            Integer businessCode = parsedResult.getInt("code", -1);
            
            if (businessCode != 0) {
                String businessMessage = parsedResult.getStr("message", "业务执行失败");
                log.error("SOAP业务执行失败，业务状态码: {}, 错误信息: {}", businessCode, businessMessage);
                throw new BusinessException(businessMessage);
            }
        } else {
            // 尝试解析原始result内容
            String result = responseData.getResult();
            if (StrUtil.isNotBlank(result) && result.trim().startsWith("{")) {
                try {
                    JSONObject jsonResult = new JSONObject(result);
                    Integer businessCode = jsonResult.getInt("code", -1);
                    
                    if (businessCode != 0) {
                        String businessMessage = jsonResult.getStr("message", "业务执行失败");
                        log.error("SOAP业务执行失败，业务状态码: {}, 错误信息: {}", businessCode, businessMessage);
                        throw new BusinessException(businessCode, businessMessage);
                    }
                } catch (Exception e) {
                    log.warn("解析业务状态码失败: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * 发送SOAP请求并解析响应（保持原有接口兼容性）
     *
     * @return 解析后的响应数据
     */
    public static ApiResult<SoapResponseVo> callSoapService() {
        // 固定参数
        String input1 = "MES0271";
        String input2 = "{\"cfxh\":\"O250529003426,O250529003503\",\"patient_id\":\"\",\"startTime\":\"\",\"endTime\":\"\",\"fg_dps\":\"0\",\"send_flag\":\"1\"}";
        
        // 调用通用方法
        SoapResponseVo responseData = callSoapServiceWithParams(input1, input2);
        
        // 检查业务状态码（保持原有行为）
        checkBusinessCode(responseData);
        
        return ApiResult.success(responseData);
    }
    
    /**
     * 构建SOAP请求XML
     *
     * @param input1 输入参数1
     * @param input2 输入参数2
     * @return SOAP请求XML字符串
     */
    private static String buildSoapRequestXml(String input1, String input2) {
        StringBuilder soapRequest = new StringBuilder();
        soapRequest.append("<?xml version=\"1.0\" encoding=\"utf-8\"?>");
        soapRequest.append("<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:dhcc=\"http://www.dhcc.com.cn\">");
        soapRequest.append("   <soapenv:Header/>");
        soapRequest.append("   <soapenv:Body>");
        soapRequest.append("      <dhcc:HIPMessageServer>");
        soapRequest.append("         <dhcc:input1><![CDATA[").append(input1).append("]]></dhcc:input1>");
        soapRequest.append("         <dhcc:input2><![CDATA[").append(input2).append("]]></dhcc:input2>");
        soapRequest.append("      </dhcc:HIPMessageServer>");
        soapRequest.append("   </soapenv:Body>");
        soapRequest.append("</soapenv:Envelope>");
        
        return soapRequest.toString();
    }
    
    /**
     * 解析SOAP响应
     *
     * @param soapResponse SOAP响应XML
     * @return 解析后的响应数据
     */
    private static SoapResponseVo parseSoapResponse(String soapResponse) {
        SoapResponseVo responseData = new SoapResponseVo();
        
        try {
            if (StrUtil.isBlank(soapResponse)) {
                throw new BusinessException(BusinessErrorEnum.SOAP_RESPONSE_FORMAT_ERROR.getCode(), "SOAP响应内容为空");
            }
            
            // 添加原始响应
            responseData.setRawResponse(soapResponse);
            
            // 解析XML响应
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document doc = builder.parse(new ByteArrayInputStream(soapResponse.getBytes(StandardCharsets.UTF_8)));
            
            // 查找响应数据节点
            NodeList responseNodes = doc.getElementsByTagNameNS("*", "HIPMessageServerResponse");
            if (responseNodes.getLength() > 0) {
                Element responseElement = (Element) responseNodes.item(0);
                
                // 提取返回值
                NodeList returnNodes = responseElement.getElementsByTagNameNS("*", "HIPMessageServerResult");
                if (returnNodes.getLength() > 0) {
                    String resultContent = returnNodes.item(0).getTextContent();
                    responseData.setResult(resultContent);
                    
                    // 尝试解析JSON格式的返回值
                    if (StrUtil.isNotBlank(resultContent) && resultContent.trim().startsWith("{")) {
                        try {
                            JSONObject jsonResult = new JSONObject(resultContent);
                            responseData.setParsedResult(jsonResult);
                        } catch (Exception e) {
                            log.warn("返回值不是有效的JSON格式: {}", resultContent);
                        }
                    }
                }
            }
            
            // 检查是否有SOAP故障
            NodeList faultNodes = doc.getElementsByTagNameNS("*", "Fault");
            if (faultNodes.getLength() > 0) {
                Element faultElement = (Element) faultNodes.item(0);
                
                NodeList faultCodeNodes = faultElement.getElementsByTagNameNS("*", "faultcode");
                NodeList faultStringNodes = faultElement.getElementsByTagNameNS("*", "faultstring");
                
                String faultCode = faultCodeNodes.getLength() > 0 ? faultCodeNodes.item(0).getTextContent() : "未知错误码";
                String faultString = faultStringNodes.getLength() > 0 ? faultStringNodes.item(0).getTextContent() : "未知错误信息";
                
                responseData.setSoapFault(true);
                responseData.setFaultCode(faultCode);
                responseData.setFaultString(faultString);
            }
            
        } catch (BusinessException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            log.error("解析SOAP响应失败", e);
            throw new BusinessException(BusinessErrorEnum.SOAP_RESPONSE_FORMAT_ERROR.getCode(), 
                    "SOAP响应解析失败: " + e.getMessage(), e);
        }
        
        return responseData;
    }

    /**
     * 获取并解析WSDL文档
     *
     * @param wsdlUrl WSDL地址
     * @return WSDL解析结果
     */
    public static JSONObject parseWsdl(String wsdlUrl) {
        JSONObject result = new JSONObject();

        try {
            HttpResponse response = HttpUtil.createGet(wsdlUrl)
                    .header("Accept", "text/xml")
                    .execute();

            if (!response.isOk()) {
                throw new BusinessException(BusinessErrorEnum.WSDL_PARSE_ERROR.getCode(),
                        "无法获取WSDL，状态码: " + response.getStatus());
            }

            String wsdlContent = response.body();
            
            // 解析WSDL
            Map<String, Object> wsdlInfo = extractWsdlInfo(wsdlContent);

            result.set("success", true);
            result.set("wsdlInfo", wsdlInfo);
            result.set("rawWsdl", wsdlContent);

        } catch (BusinessException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            log.error("WSDL解析失败", e);
            throw new BusinessException(BusinessErrorEnum.WSDL_PARSE_ERROR, e);
        }

        return result;
    }

    /**
     * 提取WSDL信息
     *
     * @param wsdlContent WSDL内容
     * @return WSDL信息
     */
    private static Map<String, Object> extractWsdlInfo(String wsdlContent) {
        Map<String, Object> wsdlInfo = new HashMap<>();

        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document doc = builder.parse(new ByteArrayInputStream(wsdlContent.getBytes()));

            // 提取服务信息
            NodeList serviceList = doc.getElementsByTagNameNS("*", "service");
            if (serviceList.getLength() > 0) {
                Element service = (Element) serviceList.item(0);
                wsdlInfo.put("serviceName", service.getAttribute("name"));
            }

            // 提取端口类型信息
            NodeList portTypeList = doc.getElementsByTagNameNS("*", "portType");
            if (portTypeList.getLength() > 0) {
                Element portType = (Element) portTypeList.item(0);
                wsdlInfo.put("portTypeName", portType.getAttribute("name"));

                // 提取操作列表
                NodeList operationList = portType.getElementsByTagNameNS("*", "operation");
                String[] operations = new String[operationList.getLength()];
                for (int i = 0; i < operationList.getLength(); i++) {
                    Element operation = (Element) operationList.item(i);
                    operations[i] = operation.getAttribute("name");
                }
                wsdlInfo.put("operations", operations);
            }

            // 提取绑定信息
            NodeList bindingList = doc.getElementsByTagNameNS("*", "binding");
            if (bindingList.getLength() > 0) {
                Element binding = (Element) bindingList.item(0);
                wsdlInfo.put("bindingName", binding.getAttribute("name"));
            }

        } catch (Exception e) {
            log.error("提取WSDL信息失败", e);
            throw new BusinessException(BusinessErrorEnum.WSDL_PARSE_ERROR.getCode(),
                    "WSDL信息提取失败: " + e.getMessage(), e);
        }

        return wsdlInfo;
    }



    /**
     * 解析SOAP响应数据为门诊处方响应VO
     *
     * @param responseData SOAP响应数据
     * @return 处方响应VO
     */
    public static OutpatientPrescriptionResponseVo parseOutpatientResponseData(SoapResponseVo responseData) {
        OutpatientPrescriptionResponseVo result = new OutpatientPrescriptionResponseVo();

        try {
            // 获取解析后的JSON结果
            JSONObject parsedResult = responseData.getParsedResult();
            if (parsedResult != null) {
                // 将JSONObject转换为处方响应VO
                String jsonString = parsedResult.toString();
                result = objectMapper.readValue(jsonString, OutpatientPrescriptionResponseVo.class);

                log.info("成功解析门诊处方响应数据: code={}, message={}, dataList.size={}",
                        result.getCode(), result.getMessage(),
                        result.getDataList() != null ? result.getDataList().size() : 0);
            } else {
                log.warn("未找到有效的解析结果，使用原始返回值");
                result.setCode(-1);
                result.setMessage("响应数据格式异常");
            }

        } catch (Exception e) {
            log.error("解析门诊处方响应数据失败", e);
            result.setCode(-1);
            result.setMessage("响应数据解析失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 解析SOAP响应数据为住院处方响应VO
     *
     * @param jsonResult json结果
     * @return 住院处方响应VO
     */
    public static InpatientPrescriptionResponseVo parseInpatientResponseData(JSONObject jsonResult) {
        InpatientPrescriptionResponseVo result = new InpatientPrescriptionResponseVo();

        try {
            // 获取解析后的JSON结果

                // 将JSONObject转换为住院处方响应VO
                String jsonString = jsonResult.toString();
                result = objectMapper.readValue(jsonString, InpatientPrescriptionResponseVo.class);

                log.info("成功解析住院处方响应数据: code={}, message={}, dataList.size={}",
                        result.getCode(), result.getMessage(),
                        result.getDataList() != null ? result.getDataList().size() : 0);

        } catch (Exception e) {
            log.error("解析住院处方响应数据失败", e);
            result.setCode(-1);
            result.setMessage("响应数据解析失败: " + e.getMessage());
        }

        return result;
    }
} 