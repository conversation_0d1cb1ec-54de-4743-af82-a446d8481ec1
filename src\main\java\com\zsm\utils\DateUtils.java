package com.zsm.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 日期工具类
 * 提供日期和日期时间的解析功能
 *
 * <AUTHOR>
 * @date 2025-01-10
 */
@Slf4j
public class DateUtils {
    
    /**
     * 解析日期字符串为LocalDate
     * 
     * @param dateStr 日期字符串
     * @return LocalDate对象，解析失败返回null
     */
    public static LocalDate parseDate(String dateStr) {
        if (!StringUtils.isNotBlank(dateStr)) {
            return null;
        }
        try {
            // 尝试解析yyyy-MM-dd格式
            return LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        } catch (Exception e) {
            try {
                // 如果解析失败，尝试解析yyyy-MM-dd HH:mm:ss格式
                final LocalDateTime parse = LocalDateTime.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                return parse.toLocalDate();
            } catch (Exception e2) {
                log.warn("日期时间解析失败: {}", dateStr);
                return null;
            }
        }
    }
    
    /**
     * 解析日期时间字符串为LocalDateTime
     * 
     * @param dateTimeStr 日期时间字符串
     * @return LocalDateTime对象，解析失败返回null
     */
    public static LocalDateTime parseDateTime(String dateTimeStr) {
        if (!StringUtils.isNotBlank(dateTimeStr)) {
            return null;
        }
        try {
            // 尝试解析yyyy-MM-dd HH:mm:ss格式
            return LocalDateTime.parse(dateTimeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        } catch (Exception e) {
            try {
                // 如果解析失败，尝试解析yyyy-MM-dd格式，并设置时间为00:00:00
                LocalDate date = LocalDate.parse(dateTimeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                return date.atStartOfDay();
            } catch (Exception e2) {
                log.warn("日期时间解析失败: {}", dateTimeStr);
                return null;
            }
        }
    }
}