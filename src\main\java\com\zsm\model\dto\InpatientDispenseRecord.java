package com.zsm.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 住院发药记录实体类
 * 对应东华MES0272接口返回数据结构
 *
 * <AUTHOR>
 * @date 2025-06-20
 */
@Data
@Schema(description = "住院发药记录")
public class InpatientDispenseRecord {

    @Schema(description = "发药记录ID")
    private String record_id;

    @Schema(description = "发药明细ID")
    private String record_detail_id;

    @Schema(description = "原明细ID")
    private String ori_detail_id;

    @Schema(description = "费用ID")
    private String id_fee;

    @Schema(description = "费用名称")
    private String na_fee;

    @Schema(description = "医嘱类别")
    private Integer sd_classify;

    @Schema(description = "拆零标识")
    private Integer fg_dps;

    @Schema(description = "发送标志")
    private Integer send_flag;

    @Schema(description = "发送时间")
    private String send_time;

    @Schema(description = "零售单据号")
    private String rtal_docno;

    @Schema(description = "销售出库单据号")
    private String stoout_no;

    @Schema(description = "病区ID")
    private String pat_ward_id;

    @Schema(description = "病区名称")
    private String pat_ward_name;

    @Schema(description = "发药科室ID")
    private String fyyf;

    @Schema(description = "科室ID")
    private String deptId;

    @Schema(description = "药师证件号码")
    private String phar_certno;

    @Schema(description = "药师姓名")
    private String phar_name;

    @Schema(description = "药师执业资格证号")
    private String phar_prac_cert_no;

    @Schema(description = "发药时间")
    private String sel_retn_time;

    @Schema(description = "HIS药品编码")
    private String hisDrugCode;

    @Schema(description = "药品目录编码")
    private String med_list_codg;

    @Schema(description = "规格")
    private String spec;

    @Schema(description = "生产企业名称")
    private String prodentp_name;

    @Schema(description = "定点医疗机构目录ID")
    private String fixmedins_hilist_id;

    @Schema(description = "定点医疗机构目录名称")
    private String fixmedins_hilist_name;

    @Schema(description = "生产批号")
    private String manu_lotnum;

    @Schema(description = "生产日期")
    private String manu_date;

    @Schema(description = "有效期")
    private String expy_end;

    @Schema(description = "批次号")
    private String bchno;

    @Schema(description = "处方标志")
    private Integer rx_flag;

    @Schema(description = "流转标志（1表示拆零药品）")
    private Integer trdn_flag;

    @Schema(description = "HIS剂量单位")
    @JsonProperty("His_dos_unit")
    private String His_dos_unit;

    @Schema(description = "HIS包装单位")
    @JsonProperty("His_pac_unit")
    private String His_pac_unit;

    @Schema(description = "HIS剂量换算比例")
    @JsonProperty("His_con_ratio")
    private String His_con_ratio;

    @Schema(description = "定点医疗机构批次号")
    private String fixmedins_bchno;

    @Schema(description = "住院号")
    private String pat_in_hos_id;

    @Schema(description = "就诊序列号")
    private String mdtrt_sn;

    @Schema(description = "患者姓名")
    private String psn_name;

    @Schema(description = "床号")
    private String bed_no;

    @Schema(description = "就诊结算类型")
    private Integer mdtrt_setl_type;

    @Schema(description = "医嘱ID")
    private String order_id;

    @Schema(description = "开方医师证件号码")
    private String prsc_dr_certno;

    @Schema(description = "开方医师姓名")
    private String prsc_dr_name;

    @Schema(description = "发药数量")
    private String sel_retn_cnt;

    @Schema(description = "发药单位")
    private String sel_retn_unit;

    @Schema(description = "处方序号")
    private Long cfxh;

    @Schema(description = "处方明细序号")
    private String cfmxxh;

    @Schema(description = "最小发药数量")
    private String min_sel_retn_cnt;

    // SAAS接口相关字段
    @Schema(description = "追溯码列表")
    private List<String> drugTracCodgs;

    @Schema(description = "发药数量")
    private String dispCnt;

    @Schema(description = "当前库存数量")
    private BigDecimal currNum;

    /**
     * 获取拆零标识
     * @return Integer
     */
    public Integer getTrdnFlag() {
        return this.trdn_flag;
    }

    /**
     * 获取处方序号字符串
     * @return String
     */
    public String getCfxh() {
        return this.cfxh != null ? this.cfxh.toString() : "";
    }

    /**
     * 获取处方明细序号
     * @return String
     */
    public String getCfmxxh() {
        return this.cfmxxh;
    }
}