package com.zsm.utils;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.zsm.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 通用HTTP请求工具类
 * 提供统一的HTTP请求方法，支持GET、POST等请求方式
 * 
 * <AUTHOR>
 * @date 2025/01/01
 */
@Slf4j
public class HttpRequestUtil {

    /**
     * 默认请求超时时间（毫秒）
     */
    private static final int DEFAULT_TIMEOUT = 30000;

    /**
     * 默认请求头 - JSON类型
     */
    private static final String CONTENT_TYPE_JSON = "application/json";
    private static final String ACCEPT_TEXT_PLAIN = "text/plain";

    /**
     * 执行POST请求（JSON格式）
     *
     * @param requestUrl    请求URL
     * @param requestParams 请求参数（将被转换为JSON）
     * @param logPrefix     日志前缀
     * @return 响应体字符串
     * @throws BusinessException 请求失败时抛出
     */
    public static String executePostRequest(String requestUrl, Map<String, Object> requestParams, String logPrefix) {
        return executePostRequest(requestUrl, requestParams, DEFAULT_TIMEOUT, logPrefix);
    }

    /**
     * 执行POST请求（JSON格式）
     *
     * @param requestUrl    请求URL
     * @param requestParams 请求参数（将被转换为JSON）
     * @param timeout       请求超时时间（毫秒）
     * @param logPrefix     日志前缀
     * @return 响应体字符串
     * @throws BusinessException 请求失败时抛出
     */
    public static String executePostRequest(String requestUrl, Map<String, Object> requestParams, int timeout, String logPrefix) {
        log.info("{}:请求地址：{}", logPrefix, requestUrl);
        log.info("{}:请求参数：{}", logPrefix, JSONUtil.toJsonStr(requestParams));

        try {
            // 使用hutool发起POST请求
            HttpResponse response = HttpRequest.post(requestUrl)
                    .header("accept", ACCEPT_TEXT_PLAIN)
                    .header("Content-Type", CONTENT_TYPE_JSON)
                    .body(JSONUtil.toJsonStr(requestParams))
                    .timeout(timeout)
                    .execute();

            String responseBody = response.body();
            log.info("{}:响应状态码：{}", logPrefix, response.getStatus());
            log.info("{}:响应数据：{}", logPrefix, responseBody);

            // 检查HTTP状态码
            if (response.getStatus() != 200) {
                throw new BusinessException("HTTP请求失败，状态码：" + response.getStatus());
            }

            return responseBody;

        } catch (Exception e) {
            log.error("{}:HTTP请求异常：{}", logPrefix, e.getMessage(), e);
            throw new BusinessException("HTTP请求异常：" + e.getMessage(), e);
        }
    }

    /**
     * 执行POST请求（自定义请求头）
     *
     * @param requestUrl    请求URL
     * @param requestBody   请求体（字符串格式）
     * @param headers       自定义请求头
     * @param timeout       请求超时时间（毫秒）
     * @param logPrefix     日志前缀
     * @return 响应体字符串
     * @throws BusinessException 请求失败时抛出
     */
    public static String executePostRequestWithHeaders(String requestUrl, String requestBody, 
                                                       Map<String, String> headers, int timeout, String logPrefix) {
        log.info("{}:请求地址：{}", logPrefix, requestUrl);
        log.info("{}:请求体：{}", logPrefix, requestBody);
        log.info("{}:请求头：{}", logPrefix, JSONUtil.toJsonStr(headers));

        try {
            HttpRequest request = HttpRequest.post(requestUrl)
                    .timeout(timeout);

            // 设置自定义请求头
            if (headers != null && !headers.isEmpty()) {
                headers.forEach(request::header);
            }

            // 设置请求体
            if (StringUtils.isNotEmpty(requestBody)) {
                request.body(requestBody);
            }

            HttpResponse response = request.execute();

            String responseBody = response.body();
            log.info("{}:响应状态码：{}", logPrefix, response.getStatus());
            log.info("{}:响应数据：{}", logPrefix, responseBody);

            // 检查HTTP状态码
            if (response.getStatus() != 200) {
                throw new BusinessException("HTTP请求失败，状态码：" + response.getStatus());
            }

            return responseBody;

        } catch (Exception e) {
            log.error("{}:HTTP请求异常：{}", logPrefix, e.getMessage(), e);
            throw new BusinessException("HTTP请求异常：" + e.getMessage(), e);
        }
    }

    /**
     * 执行GET请求
     *
     * @param requestUrl 请求URL
     * @param logPrefix  日志前缀
     * @return 响应体字符串
     * @throws BusinessException 请求失败时抛出
     */
    public static String executeGetRequest(String requestUrl, String logPrefix) {
        return executeGetRequest(requestUrl, DEFAULT_TIMEOUT, logPrefix);
    }

    /**
     * 执行GET请求
     *
     * @param requestUrl 请求URL
     * @param timeout    请求超时时间（毫秒）
     * @param logPrefix  日志前缀
     * @return 响应体字符串
     * @throws BusinessException 请求失败时抛出
     */
    public static String executeGetRequest(String requestUrl, int timeout, String logPrefix) {
        log.info("{}:请求地址：{}", logPrefix, requestUrl);

        try {
            HttpResponse response = HttpRequest.get(requestUrl)
                    .timeout(timeout)
                    .execute();

            String responseBody = response.body();
            log.info("{}:响应状态码：{}", logPrefix, response.getStatus());
            log.info("{}:响应数据：{}", logPrefix, responseBody);

            // 检查HTTP状态码
            if (response.getStatus() != 200) {
                throw new BusinessException("HTTP请求失败，状态码：" + response.getStatus());
            }

            return responseBody;

        } catch (Exception e) {
            log.error("{}:HTTP请求异常：{}", logPrefix, e.getMessage(), e);
            throw new BusinessException("HTTP请求异常：" + e.getMessage(), e);
        }
    }

    /**
     * 解析响应结果为对象列表
     *
     * @param responseBody 响应体字符串
     * @param clazz        目标类型的Class对象
     * @param <T>          目标类型
     * @return 解析后的结果列表
     * @throws BusinessException 解析失败时抛出
     */
    public static <T> List<T> parseResponseToList(String responseBody, Class<T> clazz) {
        try {
            // 解析JSON响应
            JSONObject responseJson = JSONUtil.parseObj(responseBody);

            // 检查响应是否成功（根据实际接口返回格式调整）
            if (responseJson.containsKey("code")) {
                Integer code = responseJson.getInt("code");
                if (code != null && code != 0) {
                    String message = responseJson.getStr("message", "接口返回异常");
                    throw new BusinessException("接口返回错误：" + message);
                }
            }

            // 尝试直接解析为数组格式（如果响应直接是数组）
            if (responseBody.trim().startsWith("[")) {
                return JSONUtil.toList(responseBody, clazz);
            }

            // 如果有data字段，从data字段获取数据
            if (responseJson.containsKey("data")) {
                Object data = responseJson.get("data");
                if (data != null) {
                    return JSONUtil.toList(JSONUtil.toJsonStr(data), clazz);
                }
            }

            // 如果有dataList字段，从dataList字段获取数据
            if (responseJson.containsKey("dataList")) {
                Object dataList = responseJson.get("dataList");
                if (dataList != null) {
                    return JSONUtil.toList(JSONUtil.toJsonStr(dataList), clazz);
                }
            }

            // 默认返回空列表
            log.warn("响应数据格式不符合预期，返回空列表");
            return new ArrayList<>();

        } catch (Exception e) {
            log.error("解析响应数据异常：{}", e.getMessage(), e);
            throw new BusinessException("解析响应数据失败：" + e.getMessage(), e);
        }
    }

    /**
     * 解析响应结果为对象
     *
     * @param responseBody 响应体字符串
     * @param clazz        目标类型的Class对象
     * @param <T>          目标类型
     * @return 解析后的结果对象
     * @throws BusinessException 解析失败时抛出
     */
    public static <T> T parseResponseToObject(String responseBody, Class<T> clazz) {
        try {
            // 解析JSON响应
            JSONObject responseJson = JSONUtil.parseObj(responseBody);

            // 检查响应是否成功（根据实际接口返回格式调整）
            if (responseJson.containsKey("code")) {
                Integer code = responseJson.getInt("code");
                if (code != null && code != 0) {
                    String message = responseJson.getStr("message", "接口返回异常");
                    throw new BusinessException("接口返回错误：" + message);
                }
            }

            // 如果有data字段，从data字段获取数据
            if (responseJson.containsKey("data")) {
                Object data = responseJson.get("data");
                if (data != null) {
                    return JSONUtil.toBean(JSONUtil.toJsonStr(data), clazz);
                }
            }

            // 直接解析整个响应
            return JSONUtil.toBean(responseBody, clazz);

        } catch (Exception e) {
            log.error("解析响应数据异常：{}", e.getMessage(), e);
            throw new BusinessException("解析响应数据失败：" + e.getMessage(), e);
        }
    }

    /**
     * 验证响应状态码是否为成功状态
     *
     * @param statusCode 状态码
     * @return 是否成功
     */
    public static boolean isSuccessStatusCode(int statusCode) {
        return statusCode >= 200 && statusCode < 300;
    }
} 