-- 同时新增两个字段
ALTER TABLE `ysf_sto_dps` 
ADD COLUMN `pat_ward_id` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '患者病区ID',
ADD COLUMN `pat_ward_name` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '患者病区名称';


-- 查询重复的数据

SELECT 
    drugtracinfo,
    COUNT(*) as duplicate_count,
    GROUP_CONCAT(id_tc) as duplicate_ids
FROM ysf_sto_tc 
WHERE drugtracinfo IS NOT NULL 
    AND drugtracinfo != ''
GROUP BY drugtracinfo 
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC;


-- 需要进行性能优化的表添加索引

-- YsfService.java 查询分析:
-- 1. validatePrescription() 中 ysfStoTcMapper.selectBatchByTraceinfo() 批量查询追溯码
-- 2. queryDispensingRecords() 中按多个条件查询发药单
-- 3. queryDispensingRecordDetails() 中查询发药单明细
-- 4. processTraceabilityCode() 中频繁查询追溯码状态
-- 5. 任务查询相关的多个方法

-- ysf_sto_tc 表索引优化
ALTER TABLE `ysf_sto_tc` ADD INDEX `idx_drugtracinfo` (`drugtracinfo`);
ALTER TABLE `ysf_sto_tc` ADD INDEX `idx_del_flag_drugtracinfo` (`del_flag`, `drugtracinfo`);
ALTER TABLE `ysf_sto_tc` ADD INDEX `idx_drug_code` (`drug_code`);
ALTER TABLE `ysf_sto_tc` ADD INDEX `idx_del_flag_drug_code` (`del_flag`, `drug_code`);
ALTER TABLE `ysf_sto_tc` ADD INDEX `idx_org_id_del_flag` (`org_id`, `del_flag`);

-- ysf_sto_tc_status 表索引优化
ALTER TABLE `ysf_sto_tc_status` ADD INDEX `idx_cfmxxh_drugtracinfo` (`cfmxxh`, `drugtracinfo`);
ALTER TABLE `ysf_sto_tc_status` ADD INDEX `idx_del_flag_cfmxxh` (`del_flag`, `cfmxxh`);
ALTER TABLE `ysf_sto_tc_status` ADD INDEX `idx_del_flag_drugtracinfo` (`del_flag`, `drugtracinfo`);
ALTER TABLE `ysf_sto_tc_status` ADD INDEX `idx_id_tc` (`id_tc`);
ALTER TABLE `ysf_sto_tc_status` ADD INDEX `idx_sd_tc_status` (`sd_tc_status`);

-- ysf_sto_tc_task 表索引优化
ALTER TABLE `ysf_sto_tc_task` ADD INDEX `idx_cd_biz_fg_status` (`cd_biz`, `fg_status`);
ALTER TABLE `ysf_sto_tc_task` ADD INDEX `idx_del_flag_cd_biz` (`del_flag`, `cd_biz`);
ALTER TABLE `ysf_sto_tc_task` ADD INDEX `idx_fg_status_del_flag` (`fg_status`, `del_flag`);
ALTER TABLE `ysf_sto_tc_task` ADD INDEX `idx_create_time` (`create_time`);

-- ysf_sto_tc_task_sub 表索引优化
ALTER TABLE `ysf_sto_tc_task_sub` ADD INDEX `idx_id_task_cfmxxh` (`id_task`, `cfmxxh`);
ALTER TABLE `ysf_sto_tc_task_sub` ADD INDEX `idx_id_task_fg_scanned` (`id_task`, `fg_scanned`);
ALTER TABLE `ysf_sto_tc_task_sub` ADD INDEX `idx_del_flag_cfmxxh` (`del_flag`, `cfmxxh`);
ALTER TABLE `ysf_sto_tc_task_sub` ADD INDEX `idx_cfmxxh` (`cfmxxh`);

-- ysf_sto_dps 表索引优化（已有 cfxh 索引）
ALTER TABLE `ysf_sto_dps` ADD INDEX `idx_del_flag_cfxh` (`del_flag`, `cfxh`);
ALTER TABLE `ysf_sto_dps` ADD INDEX `idx_psn_name` (`psn_name`);
ALTER TABLE `ysf_sto_dps` ADD INDEX `idx_fg_status` (`fg_status`);
ALTER TABLE `ysf_sto_dps` ADD INDEX `idx_id_dept` (`id_dept`);
ALTER TABLE `ysf_sto_dps` ADD INDEX `idx_pat_ward_id` (`pat_ward_id`);
ALTER TABLE `ysf_sto_dps` ADD INDEX `idx_sd_dps` (`sd_dps`);
ALTER TABLE `ysf_sto_dps` ADD INDEX `idx_send_time` (`send_time`);
ALTER TABLE `ysf_sto_dps` ADD INDEX `idx_fg_dps_del_flag` (`fg_dps`, `del_flag`);
ALTER TABLE `ysf_sto_dps` ADD INDEX `idx_send_time_id_dps` (`send_time`, `id_dps`);

-- ysf_sto_dps_sub 表索引优化
ALTER TABLE `ysf_sto_dps_sub` ADD INDEX `idx_id_dps_cfmxxh` (`id_dps`, `cfmxxh`);
ALTER TABLE `ysf_sto_dps_sub` ADD INDEX `idx_del_flag_cfmxxh` (`del_flag`, `cfmxxh`);
ALTER TABLE `ysf_sto_dps_sub` ADD INDEX `idx_cfxh` (`cfxh`);
ALTER TABLE `ysf_sto_dps_sub` ADD INDEX `idx_del_flag_cfxh` (`del_flag`, `cfxh`);
ALTER TABLE `ysf_sto_dps_sub` ADD INDEX `idx_na_fee` (`na_fee`);
ALTER TABLE `ysf_sto_dps_sub` ADD INDEX `idx_drug_code` (`drug_code`);

-- nhsa_3505 表额外索引优化（已有部分索引）
ALTER TABLE `nhsa_3505` ADD INDEX `idx_out_pres_id` (`out_pres_id`);
ALTER TABLE `nhsa_3505` ADD INDEX `idx_cfxh` (`cfxh`);
ALTER TABLE `nhsa_3505` ADD INDEX `idx_cfmxxh` (`cfmxxh`);
ALTER TABLE `nhsa_3505` ADD INDEX `idx_create_time` (`create_time`);
ALTER TABLE `nhsa_3505` ADD INDEX `idx_delete_flag` (`delete_flag`);
ALTER TABLE `nhsa_3505` ADD INDEX `idx_his_drug_id` (`his_drug_id`);
ALTER TABLE `nhsa_3505` ADD INDEX `idx_return_status` (`return_status`);

-- 额外的复合索引优化，针对常用查询组合
ALTER TABLE `ysf_sto_dps` ADD INDEX `idx_del_flag_fg_status_send_time` (`del_flag`, `fg_status`, `send_time`);
ALTER TABLE `ysf_sto_tc_task` ADD INDEX `idx_del_flag_fg_status_create_time` (`del_flag`, `fg_status`, `create_time`);
ALTER TABLE `nhsa_3505` ADD INDEX `idx_delete_flag_medical_code_create_time` (`delete_flag`, `medical_code`, `create_time`);


-- =================================================================
-- 表结构同步脚本 - 根据git提交同步阜阳人医退药和静配发药表结构
-- 生成时间: 根据最新的schema.sql文件变更
-- =================================================================

-- 1. 修改 nhsa_3505 表结构
-- 修改字段注释：云闪付 → 云速付
ALTER TABLE `nhsa_3505` MODIFY COLUMN `ymf_user_id` bigint NULL DEFAULT NULL COMMENT '云速付用户ID';
ALTER TABLE `nhsa_3505` MODIFY COLUMN `ymf_user_name` varchar(30) NULL DEFAULT NULL COMMENT '云速付用户名';
ALTER TABLE `nhsa_3505` MODIFY COLUMN `ymf_nick_name` varchar(30) NULL DEFAULT NULL COMMENT '云速付昵称';

-- 修改sjh字段注释：手机号 → 收据号
ALTER TABLE `nhsa_3505` MODIFY COLUMN `sjh` varchar(50) NULL DEFAULT NULL COMMENT '收据号';

-- 新增字段：dept_id（科室ID）
ALTER TABLE `nhsa_3505` ADD COLUMN `dept_id` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '科室ID';

-- 新增字段：sd_dps（发药单类型）
ALTER TABLE `nhsa_3505` ADD COLUMN `sd_dps` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发药单类型;1: 住院处方, 2:门诊处方';

-- 2. 修改 nhsa_3506 表结构
-- 新增云速付相关字段
ALTER TABLE `nhsa_3506` ADD COLUMN `ymf_user_id` bigint DEFAULT NULL COMMENT '云速付用户ID';
ALTER TABLE `nhsa_3506` ADD COLUMN `ymf_user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '云速付用户名';
ALTER TABLE `nhsa_3506` ADD COLUMN `ymf_nick_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '云速付昵称';

-- 新增处方相关字段
ALTER TABLE `nhsa_3506` ADD COLUMN `cfxh` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '处方序号';
ALTER TABLE `nhsa_3506` ADD COLUMN `cfmxxh` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '处方明细序号';

-- 3. 修改 ysf_sto_dps 表结构
-- 扩展病区名称字段长度：24 → 100
ALTER TABLE `ysf_sto_dps` MODIFY COLUMN `pat_ward_name` varchar(100) NULL DEFAULT NULL COMMENT '病区名称';

-- 新增患者唯一住院号字段
ALTER TABLE `ysf_sto_dps` ADD COLUMN `pat_in_hos_id` varchar(100) NULL DEFAULT NULL COMMENT '患者唯一住院号';

-- 4. 修改 ysf_sto_dps_sub 表结构
-- 新增患者相关字段
ALTER TABLE `ysf_sto_dps_sub` ADD COLUMN `pat_in_hos_id` varchar(100) NULL DEFAULT NULL COMMENT '患者唯一住院号';
ALTER TABLE `ysf_sto_dps_sub` ADD COLUMN `patient_id` varchar(24) NULL DEFAULT NULL COMMENT '患者ID';
ALTER TABLE `ysf_sto_dps_sub` ADD COLUMN `psn_name` varchar(32) NULL DEFAULT NULL COMMENT '患者名称';

-- 修改表注释：发药单明细 → 门诊发药单明细
ALTER TABLE `ysf_sto_dps_sub` COMMENT = '门诊发药单明细';

-- 5. 创建新表 ysf_sto_dps_sub_hos（住院发药单明细）
CREATE TABLE IF NOT EXISTS `ysf_sto_dps_sub_hos` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '发药单明细ID',
  `cfmxxh` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '处方明细序号',
  `ori_cfmxxh` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '原发药单处方明细序号;用于退药单',
  `ori_id` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '原发药单明细ID;用于退药单，原发药单明细id',
  `cfxh` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '发药单序号',
  `id_dps` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '发药单ID',
  `id_fee` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '费用明细ID',
  `na_fee` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '费用名称',
  `drug_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '药品编码',
  `drugtracinfo` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '追溯码',
  `price_sale` decimal(24,6) DEFAULT NULL COMMENT '单价',
  `sel_retn_cnt` int DEFAULT NULL COMMENT '数量',
  `amt_total` decimal(24,6) DEFAULT NULL COMMENT '金额',
  `amt_total_dps` decimal(24,6) DEFAULT NULL COMMENT '实发金额',
  `unit_sale` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '包装单位',
  `unit_sale_factor` int DEFAULT NULL COMMENT '包装系数',
  `amt_total_pur` decimal(24,6) DEFAULT NULL COMMENT '发药时的进货总价',
  `id_org` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '机构编号',
  `org_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '医疗机构编码',
  `org_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '医疗机构名称',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  `quantity` int DEFAULT NULL COMMENT '药品数量',
  `unit` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '药品单位',
  `trac_cnt` int DEFAULT NULL COMMENT '已采集的追溯码数量',
  `pat_in_hos_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '患者唯一住院号',
  `patient_id` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '患者ID',
  `psn_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '患者名称',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='住院发药单明细';

-- 6. 为新增字段添加索引（可选，根据实际查询需求）
-- 为nhsa_3505新增字段添加索引
ALTER TABLE `nhsa_3505` ADD INDEX `idx_dept_id` (`dept_id`);
ALTER TABLE `nhsa_3505` ADD INDEX `idx_sd_dps` (`sd_dps`);

-- 为nhsa_3506新增字段添加索引
ALTER TABLE `nhsa_3506` ADD INDEX `idx_cfxh` (`cfxh`);
ALTER TABLE `nhsa_3506` ADD INDEX `idx_cfmxxh` (`cfmxxh`);
ALTER TABLE `nhsa_3506` ADD INDEX `idx_ymf_user_id` (`ymf_user_id`);

-- 为ysf_sto_dps新增字段添加索引
ALTER TABLE `ysf_sto_dps` ADD INDEX `idx_pat_in_hos_id` (`pat_in_hos_id`);

-- 为ysf_sto_dps_sub新增字段添加索引
ALTER TABLE `ysf_sto_dps_sub` ADD INDEX `idx_pat_in_hos_id` (`pat_in_hos_id`);
ALTER TABLE `ysf_sto_dps_sub` ADD INDEX `idx_patient_id` (`patient_id`);
ALTER TABLE `ysf_sto_dps_sub` ADD INDEX `idx_psn_name` (`psn_name`);

-- =================================================================
-- 脚本执行完成
-- 注意事项：
-- 1. 请在非生产环境先测试此脚本
-- 2. 建议在业务低峰期执行表结构变更
-- 3. 大表字段修改可能耗时较长，请提前规划
-- 4. 执行前请做好数据备份
-- =================================================================


