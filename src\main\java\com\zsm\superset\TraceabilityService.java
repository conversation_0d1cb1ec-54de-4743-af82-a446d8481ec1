package com.zsm.superset;

import com.zsm.model.ApiResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 上传统计数据到SuperSet平台的服务类
 *
 * <AUTHOR>
 * @date 2025/6/5 下午5:14
 */
@Slf4j
@Service
public class TraceabilityService {

    @Resource
    private Nhsa3505YmfUserCountAsync nhsa3505YmfUserCountAsync;
    @Resource
    private Nhsa3505CntCountAsync nhsa3505CntCountAsync;


    public ApiResult<String> generateAndUploadToSuperSet() {
        try {
            String startTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            log.info("开始执行SuperSet数据统计上传任务，开始时间：{}", startTime);
            
            nhsa3505CntCountAsync.countNhsa3505CntCount();
            nhsa3505YmfUserCountAsync.nhsa3505YmfUserCount();
            
            String endTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            String result = String.format("SuperSet数据统计上传任务执行成功！开始时间：%s，结束时间：%s", startTime, endTime);
            log.info(result);
            return ApiResult.success(result);
        } catch (Exception e) {
            String errorMsg = "SuperSet数据统计上传任务执行失败：" + e.getMessage();
            log.error(errorMsg, e);
            return ApiResult.error(errorMsg);
        }
    }

    /**
     * 生成并上传统计数据到SuperSet平台
     * @param startDate 开始日期，格式：yyyy-MM-dd，为null时默认为今天
     * @param endDate 结束日期，格式：yyyy-MM-dd，为null时默认为今天
     * @return 执行结果信息
     */
    public ApiResult<String> generateAndUploadToSuperSet(String startDate, String endDate) {
        try {
            String actualStartDate = (startDate == null || startDate.trim().isEmpty()) ? "今天" : startDate;
            String actualEndDate = (endDate == null || endDate.trim().isEmpty()) ? "今天" : endDate;
            String startTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            
            log.info("开始执行SuperSet数据统计上传任务，日期范围：{} 至 {}，开始时间：{}", actualStartDate, actualEndDate, startTime);
            
            nhsa3505CntCountAsync.countNhsa3505CntCount(startDate, endDate);
            nhsa3505YmfUserCountAsync.nhsa3505YmfUserCount(startDate, endDate);
            
            String endTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            String result = String.format("SuperSet数据统计上传任务执行成功！日期范围：%s 至 %s，开始时间：%s，结束时间：%s", 
                    actualStartDate, actualEndDate, startTime, endTime);
            log.info(result);
            return ApiResult.success(result);
        } catch (Exception e) {
            String errorMsg = String.format("SuperSet数据统计上传任务执行失败，日期范围：%s 至 %s，错误信息：%s", 
                    startDate, endDate, e.getMessage());
            log.error(errorMsg, e);
            return ApiResult.error(errorMsg);
        }
    }
}
