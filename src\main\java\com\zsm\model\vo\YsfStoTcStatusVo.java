package com.zsm.model.vo;

import com.zsm.entity.YsfStoTcStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 追溯码流转记录VO
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "追溯码流转记录VO")
public class YsfStoTcStatusVo extends YsfStoTcStatus {

    private static final long serialVersionUID = 1L;

    /**
     * 业务类型名称
     */
    @Schema(description = "业务类型名称")
    private String sdTcStatusName;

    /**
     * 库房名称
     */
    @Schema(description = "库房名称")
    private String stoName;
} 