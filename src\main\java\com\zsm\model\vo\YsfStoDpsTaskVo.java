package com.zsm.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 扫码任务关联发药单信息VO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "扫码任务关联发药单信息VO")
public class YsfStoDpsTaskVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 患者ID
     */
    @Schema(description = "患者ID")
    private String patientId;

    /**
     * 处方序号
     */
    @Schema(description = "处方序号")
    private String cfxh;

    /**
     * 任务创建时间
     */
    @Schema(description = "任务创建时间")
    private Date createTime;

    /**
     * 发药单类型;1: 住院处方, 2:门诊处方
     */
    @Schema(description = "发药单类型;1: 住院处方, 2:门诊处方")
    private String sdDps;
} 