package com.zsm.common.exception;

import lombok.Getter;

/**
 * 业务异常类
 * 用于处理业务逻辑中的异常情况，包含错误码和错误消息
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
public class BusinessException extends RuntimeException {

    /**
     * 错误码
     */
    private final Integer code;

    /**
     * 错误消息
     */
    private final String message;
    /**
     * 传递的对象
     */
    private Object object;

    /**
     * 构造函数
     *
     * @param message 错误消息
     */
    public BusinessException(String message) {
        super(message);
        this.code = 500;
        this.message = message;
    }

    /**
     * 构造函数,带有传递的对象
     *
     * @param message 错误消息
     */
    public BusinessException(String message, Object object) {
        super(message);
        this.code = 500;
        this.message = message;
        this.object = object;
    }

    /**
     * 构造函数
     *
     * @param code    错误码
     * @param message 错误消息
     */
    public BusinessException(Integer code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    /**
     * 构造函数
     *
     * @param code    错误码
     * @param message 错误消息
     * @param cause   原因异常
     */
    public BusinessException(Integer code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.message = message;
    }

    /**
     * 使用枚举构造业务异常
     *
     * @param errorEnum 错误枚举
     */
    public BusinessException(BusinessErrorEnum errorEnum) {
        super(errorEnum.getMessage());
        this.code = errorEnum.getCode();
        this.message = errorEnum.getMessage();
    }

    /**
     * 使用枚举构造业务异常，带原因异常
     *
     * @param errorEnum 错误枚举
     * @param cause     原因异常
     */
    public BusinessException(BusinessErrorEnum errorEnum, Throwable cause) {
        super(errorEnum.getMessage(), cause);
        this.code = errorEnum.getCode();
        this.message = errorEnum.getMessage();
    }


    /**
     * 静态工厂方法 - 创建业务异常
     *
     * @param code    错误码
     * @param message 错误消息
     * @return 业务异常实例
     */
    public static BusinessException of(Integer code, String message) {
        return new BusinessException(code, message);
    }

    /**
     * 静态工厂方法 - 使用枚举创建业务异常
     *
     * @param errorEnum 错误枚举
     * @return 业务异常实例
     */
    public static BusinessException of(BusinessErrorEnum errorEnum) {
        return new BusinessException(errorEnum);
    }

    @Override
    public String toString() {
        return "BusinessException{" +
                "code=" + code +
                ", message='" + message + '\'' +
                '}';
    }
} 