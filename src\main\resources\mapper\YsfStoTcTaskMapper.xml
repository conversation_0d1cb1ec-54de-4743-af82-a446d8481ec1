<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsm.mapper.YsfStoTcTaskMapper">
<!-- 查询扫码任务列表 -->
    <select id="selectTaskList" parameterType="com.zsm.model.dto.YsfStoTcTaskQueryDto" resultType="com.zsm.entity.YsfStoTcTask">
        SELECT
            *
        FROM
            ysf_sto_tc_task
        <where>
            del_flag = '0'
            <if test="queryDto.taskStatus != null and queryDto.taskStatus != ''">
                AND fg_status = #{queryDto.taskStatus}
            </if>
            <if test="queryDto.remark != null and queryDto.remark != ''">
                AND remark LIKE CONCAT('%', #{queryDto.remark}, '%')
            </if>
            <if test="queryDto.bizCode != null and queryDto.bizCode != ''">
                AND cd_biz = #{queryDto.bizCode}
            </if>
            <if test="queryDto.taskType != null and queryDto.taskType != ''">
                AND sd_task_type = #{queryDto.taskType}
            </if>
            <if test="queryDto.startTime != null">
                AND create_time &gt;= #{queryDto.startTime}
            </if>
            <if test="queryDto.endTime != null">
                AND create_time &lt;= #{queryDto.endTime}
            </if>
        </where>
        ORDER BY id_task DESC
    </select>

    <!-- 查询待处理任务关联的发药单患者信息和处方序号 -->
    <select id="selectDpsInfoByPendingTask" resultType="com.zsm.model.vo.YsfStoDpsTaskVo">
        SELECT
            d.patient_id,
            d.cfxh,
            t.create_time,
            d.sd_dps
        FROM
            ysf_sto_tc_task t
        INNER JOIN
            ysf_sto_dps d ON t.id_task = d.id_task
        WHERE
            t.fg_status = '0'
            AND t.del_flag = '0'
            AND d.del_flag = '0'
    </select>

    <!-- 批量根据处方号查询最新任务 -->
    <select id="selectLatestByCfxh" resultType="com.zsm.entity.YsfStoTcTask">
        SELECT 
            t1.*
        FROM ysf_sto_tc_task t1
        INNER JOIN (
            SELECT 
                cd_biz,
                MAX(create_time) as max_create_time
            FROM ysf_sto_tc_task
            WHERE cd_biz IN 
            <foreach collection="cfxhList" item="cfxh" open="(" separator="," close=")">
                #{cfxh}
            </foreach>
            AND del_flag = '0'
            GROUP BY cd_biz
        ) t2 ON t1.cd_biz = t2.cd_biz AND t1.create_time = t2.max_create_time
        WHERE t1.del_flag = '0'
    </select>
</mapper>
