package com.zsm.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsm.entity.YsfStoWinLimit;
import com.zsm.mapper.YsfStoWinLimitMapper;
import com.zsm.service.YsfStoWinLimitService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 药房窗口限定 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-02
 */
@Service
public class YsfStoWinLimitServiceImpl extends ServiceImpl<YsfStoWinLimitMapper, YsfStoWinLimit> implements YsfStoWinLimitService {

}
