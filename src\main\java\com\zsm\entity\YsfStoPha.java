package com.zsm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 发药药房设置实体类
 * 
 * <AUTHOR>
 * @since 2025-06-02
 */
@Data
@TableName("ysf_sto_pha")
@Schema(description = "发药药房设置")
public class YsfStoPha implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 类别;rbmh.base.med.articleType,1西药、2中成药、3草药、4疫苗、5耗材、6固定资产,7.民族药品,8.院内制剂
     */
    @Schema(description = "类别;rbmh.base.med.articleType,1西药、2中成药、3草药、4疫苗、5耗材、6固定资产,7.民族药品,8.院内制剂")
    @Size(max = 100, message = "类别;rbmh.base.med.articleType,1西药、2中成药、3草药、4疫苗、5耗材、6固定资产,7.民族药品,8.院内制剂长度不能超过100个字符")
    @TableField("sd_disp")
    private String sdDisp;

    /**
     * 仓储id
     */
    @Schema(description = "仓储id")
    @Size(max = 100, message = "仓储id长度不能超过100个字符")
    @TableField("id_sto")
    private String idSto;

    /**
     * 科室id
     */
    @Schema(description = "科室id")
    @Size(max = 100, message = "科室id长度不能超过100个字符")
    @TableField("id_dept")
    private String idDept;

    /**
     * 使用范围;1.门诊,2.住院,3.全部
     */
    @Schema(description = "使用范围;1.门诊,2.住院,3.全部")
    @Size(max = 100, message = "使用范围;1.门诊,2.住院,3.全部长度不能超过100个字符")
    @TableField("sd_use")
    private String sdUse;

    /**
     * 其他限定;21常规,24急诊用药,25.出院带药
     */
    @Schema(description = "其他限定;21常规,24急诊用药,25.出院带药")
    @Size(max = 100, message = "其他限定;21常规,24急诊用药,25.出院带药长度不能超过100个字符")
    @TableField("sd_limit")
    private String sdLimit;

    /**
     * 机构编号
     */
    @Schema(description = "机构编号")
    @Size(max = 100, message = "机构编号长度不能超过100个字符")
    @TableField("id_org")
    private String idOrg;

    /**
     * 医疗机构编码
     */
    @Schema(description = "医疗机构编码")
    @Size(max = 100, message = "医疗机构编码长度不能超过100个字符")
    @TableField("org_id")
    private String orgId;

    /**
     * 医疗机构名称
     */
    @Schema(description = "医疗机构名称")
    @NotBlank(message = "医疗机构名称不能为空")
    @Size(max = 30, message = "医疗机构名称长度不能超过30个字符")
    @TableField("org_name")
    private String orgName;

    /**
     * 乐观锁
     */
    @Schema(description = "乐观锁")
    @Size(max = 100, message = "乐观锁长度不能超过100个字符")
    @TableField("revision")
    private String revision;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    @Size(max = 100, message = "创建者长度不能超过100个字符")
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    @Size(max = 100, message = "更新者长度不能超过100个字符")
    @TableField("update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @TableField("remark")
    private String remark;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @Schema(description = "删除标志（0代表存在 1代表删除）")
    @Size(max = 100, message = "删除标志（0代表存在 1代表删除）长度不能超过100个字符")
    @TableField("del_flag")
    private String delFlag;
} 
