package com.zsm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 追溯码状态记录表实体类
 * 
 * <AUTHOR>
 * @since 2025-06-02
 */
@Data
@TableName("ysf_sto_tc_status")
@Schema(description = "追溯码状态记录表")
public class YsfStoTcStatus implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 业务类型
     */
    @Schema(description = "业务类型")
    @Size(max = 100, message = "业务类型长度不能超过100个字符")
    @TableField("sd_tc_status")
    private String sdTcStatus;

    /**
     * 追溯码管理模式;数据落地时的追溯码管理模式,简易管理/严格管理
     */
    @Schema(description = "追溯码管理模式;数据落地时的追溯码管理模式,简易管理/严格管理")
    @Size(max = 100, message = "追溯码管理模式;数据落地时的追溯码管理模式,简易管理/严格管理长度不能超过100个字符")
    @TableField("sd_tc_manage")
    private String sdTcManage;

    /**
     * 发药单明细id(原始业务主键)
     */
    @Schema(description = "发药单明细id(原始业务主键)")
    @TableField("id_biz_ori")
    private String idBizOri;

    /**
     * 追溯码主键
     */
    @Schema(description = "追溯码主键")
    @TableField("id_tc")
    private Long idTc;

    /**
     * 原始业务明细编号
     */
    @Schema(description = "原始业务明细编号")
    @Size(max = 100, message = "原始业务明细编号长度不能超过100个字符")
    @TableField("cfmxxh")
    private String cfmxxh;

    /**
     * 商品编码
     */
    @Schema(description = "商品编码")
    @Size(max = 100, message = "商品编码长度不能超过100个字符")
    @TableField("drug_code")
    private String drugCode;

    /**
     * 追溯码
     */
    @Schema(description = "追溯码")
    @Size(max = 100, message = "追溯码长度不能超过100个字符")
    @TableField("drugtracinfo")
    private String drugtracinfo;

    /**
     * 库存id;预留字段,后期扩展用
     */
    @Schema(description = "库存id;预留字段,后期扩展用")
    @Size(max = 100, message = "库存id;预留字段,后期扩展用长度不能超过100个字符")
    @TableField("id_sto_inv")
    private String idStoInv;

    /**
     * 库房id;预留字段,后期扩展用
     */
    @Schema(description = "库房id;预留字段,后期扩展用")
    @Size(max = 100, message = "库房id;预留字段,后期扩展用长度不能超过100个字符")
    @TableField("id_dept")
    private String idDept;

    /**
     * 追溯码类型;1.整箱码,2.商品追溯码.
     */
    @Schema(description = "追溯码类型;1.整箱码,2.商品追溯码.")
    @Size(max = 100, message = "追溯码类型;1.整箱码,2.商品追溯码.长度不能超过100个字符")
    @TableField("sd_tc")
    private String sdTc;

    /**
     * 数量;针对于sd_tc=1的记录,记录下商品的数量
     */
    @Schema(description = "数量;针对于sd_tc=1的记录,记录下商品的数量")
    @TableField("sel_retn_cnt")
    private Integer selRetnCnt;

    /**
     * 是否拆包装:0未拆零,1拆零
     */
    @Schema(description = "是否拆包装:0未拆零,1拆零")
    @Size(max = 100, message = "是否拆包装长度不能超过100个字符")
    @TableField("fg_pack")
    private String fgPack;

    /**
     * 上传标志
     */
    @Schema(description = "上传标志")
    @Size(max = 100, message = "上传标志长度不能超过100个字符")
    @TableField("fg_up")
    private String fgUp;

    /**
     * 确认标志;0未确认,1已确认,用于调拨申领
     */
    @Schema(description = "确认标志;0未确认,1已确认,用于调拨申领")
    @Size(max = 100, message = "确认标志;0未确认,1已确认,用于调拨申领长度不能超过100个字符")
    @TableField("fg_active")
    private String fgActive;

    /**
     * 机构编号
     */
    @Schema(description = "机构编号")
    @Size(max = 100, message = "机构编号长度不能超过100个字符")
    @TableField("id_org")
    private String idOrg;

    /**
     * 医疗机构编码
     */
    @Schema(description = "医疗机构编码")
    @Size(max = 100, message = "医疗机构编码长度不能超过100个字符")
    @TableField("org_id")
    private String orgId;

    /**
     * 医疗机构名称
     */
    @Schema(description = "医疗机构名称")
    @NotBlank(message = "医疗机构名称不能为空")
    @Size(max = 30, message = "医疗机构名称长度不能超过30个字符")
    @TableField("org_name")
    private String orgName;

    /**
     * 乐观锁
     */
    @Schema(description = "乐观锁")
    @Size(max = 100, message = "乐观锁长度不能超过100个字符")
    @TableField("revision")
    private String revision;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    @Size(max = 100, message = "创建者长度不能超过100个字符")
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    @Size(max = 100, message = "更新者长度不能超过100个字符")
    @TableField("update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @TableField("remark")
    private String remark;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @Schema(description = "删除标志（0代表存在 1代表删除）")
    @Size(max = 100, message = "删除标志（0代表存在 1代表删除）长度不能超过100个字符")
    @TableField("del_flag")
    private String delFlag;
} 
