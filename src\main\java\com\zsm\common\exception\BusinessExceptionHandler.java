package com.zsm.common.exception;

import java.lang.annotation.*;

/**
 * 业务异常处理注解
 * 可以标注在方法或类上，用于声明该方法或类中可能抛出的业务异常类型
 * 主要用于文档化和IDE提示，便于开发者了解方法可能抛出的业务异常
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface BusinessExceptionHandler {

    /**
     * 可能抛出的业务异常类型
     * 支持多个异常枚举类型，用于声明该方法或类可能抛出的所有业务异常
     *
     * @return 业务异常枚举数组
     */
    BusinessErrorEnum[] value() default {};

    /**
     * 异常处理描述信息
     * 用于描述异常处理的相关信息，便于开发者理解
     *
     * @return 描述信息
     */
    String description() default "";

    /**
     * 是否启用异常处理
     * 可以用于开关某个方法或类的业务异常处理功能
     *
     * @return 是否启用，默认为true
     */
    boolean enabled() default true;

    /**
     * 异常处理优先级
     * 当多个处理器存在时，优先级高的会被优先处理
     * 数值越小优先级越高
     *
     * @return 优先级，默认为0
     */
    int order() default 0;
} 