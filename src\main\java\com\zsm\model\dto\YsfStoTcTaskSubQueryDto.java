package com.zsm.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 扫码任务明细查询请求参数
 *
 * <AUTHOR>
 * @date 2025/06/03
 */
@Data
@Schema(description = "扫码任务明细查询请求参数")
public class YsfStoTcTaskSubQueryDto {

    @NotNull(message = "扫码任务ID不能为空")
    @Schema(description = "扫码任务ID (id_task)", required = true)
    private Long idTask;

    @Schema(description = "药品名称或编码")
    private String drugNameOrCode;

    @Schema(description = "是否已扫码", example = "0")
    private String isScanned;

    @Schema(description = "追溯码")
    private String traceCode;
    
    @NotNull(message = "页码不能为空")
    @Schema(description = "页码", example = "1")
    private Integer pageNum;

    @NotNull(message = "每页数量不能为空")
    @Schema(description = "每页数量", example = "10")
    private Integer pageSize;
} 