package com.zsm.common;

import cn.hutool.json.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.io.InputStreamSource;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.mvc.method.annotation.ExtendedServletRequestDataBinder;

import jakarta.servlet.http.HttpServletRequest;
import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Enumeration;

/**
 * 输出接口层的出入参信息日志打印
 * aop切片日志
 */
@Aspect
@Slf4j
@Component
public class ConsoleLogAspect {

    /**
     * 处理返回结果并记录日志
     *
     * @param result     结果
     * @param startTime  开始时间
     * @param logBuilder 日志构建器
     * @return {@link Object }
     */
    private Object resultVoid(Object result, long startTime, StringBuilder logBuilder) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
            String startTimeStr = sdf.format(new Date(startTime));
            String endTimeStr = sdf.format(new Date());
            long duration = System.currentTimeMillis() - startTime;

            logBuilder.append("出参 : ");
            if (result != null) {
                String resultJson = new JSONObject(result).toString();
                
                logBuilder.append(resultJson);
            } else {
                logBuilder.append("null");
            }

            logBuilder.append("\n请求耗时: ")
                    .append(duration)
                    .append("ms | 请求时间: ")
                    .append(startTimeStr)
                    .append(" | 响应时间: ")
                    .append(endTimeStr)
                    .append("\n")
                    .append("================================================================================");

            log.info(logBuilder.toString());
        } catch (Exception e) {
            log.error("记录返回结果异常: {}", e.getMessage());
        }
        return result;
    }

    // 定义一个切点
    @Pointcut("execution(public * com.zsm.controller..*(..))")
    public void controllerPointcut() {
    }

    @Before("controllerPointcut()")
    public void doBefore(JoinPoint joinPoint) {
        // 预留方法，可用于前置处理
    }

    @Around("controllerPointcut()")
    public Object doAround(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        StringBuilder logBuilder = new StringBuilder();
        ServletRequestAttributes attributes = null;
        HttpServletRequest request = null;
        String requestMethod = "";
        Object result = null;

        try {
            // 获取请求上下文信息
            attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes == null) {
                logBuilder.append("警告：无法获取请求上下文信息\n");
                result = proceedingJoinPoint.proceed();
                return resultVoid(result, startTime, logBuilder);
            }

            request = attributes.getRequest();

            // 获取方法签名信息
            MethodSignature signature = (MethodSignature) proceedingJoinPoint.getSignature();
            String methodName = signature.getName();
            requestMethod = request.getMethod();

            // 获取远程IP
            String remoteIp = getRemoteIp(request);

            // 获取API操作注解信息
            Method method = signature.getMethod();
            String apiOperationValue = getApiOperationValue(method);

            // 构建基础日志信息
            buildBasicLogInfo(logBuilder, request, signature, methodName, remoteIp, apiOperationValue);

            // 提取请求参数
            extractRequestParams(proceedingJoinPoint, request, requestMethod, logBuilder);

            // 执行原方法
            result = proceedingJoinPoint.proceed();

            // 处理返回结果并记录日志
            return resultVoid(result, startTime, logBuilder);
        } catch (Throwable e) {
            // 记录异常信息
            logBuilder.append("异常信息：\n")
                    .append("异常类型：")
                    .append(e.getClass()
                            .getName())
                    .append("\n")
                    .append("异常消息：")
                    .append(e.getMessage())
                    .append("\n")
                    .append("堆栈跟踪：\n");

            // 添加堆栈跟踪信息（限制长度）
            StackTraceElement[] stackTrace = e.getStackTrace();
            int maxStackLines = 10; // 限制堆栈输出行数
            for (int i = 0; i < Math.min(maxStackLines, stackTrace.length); i++) {
                logBuilder.append("  at ")
                        .append(stackTrace[i].toString())
                        .append("\n");
            }
            if (stackTrace.length > maxStackLines) {
                logBuilder.append("  ... ")
                        .append(stackTrace.length - maxStackLines)
                        .append(" more\n");
            }

            // 记录异常日志
            log.error(logBuilder.toString());

            // 重新抛出异常
            throw e;
        }
    }

    /**
     * 获取API操作注解值
     *
     * @param method 方法
     * @return API操作值
     */
    private String getApiOperationValue(Method method) {
        try {
            // 方法1：直接获取@Operation注解
            Operation operation = method.getAnnotation(Operation.class);
            if (operation != null) {
                return operation.summary();
            }

            // 方法2：如果使用的是其他版本的Swagger注解，可以尝试反射方式
            Annotation[] annotations = method.getAnnotations();
            for (Annotation annotation : annotations) {
                String annotationName = annotation.annotationType()
                        .getName();
                if (annotationName.contains("Operation")) {
                    try {
                        // 使用反射获取summary方法
                        Method summaryMethod = annotation.getClass()
                                .getMethod("summary");
                        String summary = (String) summaryMethod.invoke(annotation);
                        if (summary != null && !summary.trim()
                                .isEmpty()) {
                            return summary;
                        }
                    } catch (Exception e) {
                        log.error("通过反射获取summary失败: {}", e.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            log.warn("获取API操作注解异常: {}", e.getMessage());
        }
        return "";
    }

    /**
     * 构建基础日志信息
     */
    private void buildBasicLogInfo(StringBuilder logBuilder, HttpServletRequest request,
                                   MethodSignature signature, String methodName,
                                   String remoteIp, String apiOperationValue) {
        logBuilder.append("\n请求接口 : ")
                .append(request.getMethod())
                .append(" ")
                .append(request.getRequestURL())
                .append("\n")
                .append("方法名称 : ")
                .append(apiOperationValue)
                .append("\n")
                .append("接口方法 : ")
                .append(signature.getDeclaringTypeName())
                .append(".")
                .append(methodName)
                .append("\n")
                .append("设备ip : ")
                .append(remoteIp)
                .append("\n");
    }

    /**
     * 提取请求参数
     */
    private void extractRequestParams(ProceedingJoinPoint proceedingJoinPoint,
                                      HttpServletRequest request,
                                      String requestMethod,
                                      StringBuilder logBuilder) {
        try {
            // 获取URL参数
            StringBuilder urlParams = extractUrlParameters(request);

            // 获取请求体参数
            Object[] args = proceedingJoinPoint.getArgs();

            if ("POST".equalsIgnoreCase(requestMethod) || "PUT".equalsIgnoreCase(requestMethod)) {
                handlePostOrPutRequest(args, urlParams, logBuilder, requestMethod);
            } else if ("GET".equalsIgnoreCase(requestMethod) || "DELETE".equalsIgnoreCase(requestMethod)) {
                handleGetOrDeleteRequest(urlParams, logBuilder);
            } else {
                logBuilder.append(requestMethod)
                        .append("入参 ：");
                if (urlParams.length() > 0) {
                    logBuilder.append(urlParams)
                            .append("\n");
                } else {
                    logBuilder.append("无入参\n");
                }
            }
        } catch (Exception e) {
            log.error("提取请求参数异常: {}", e.getMessage(), e);
            logBuilder.append("参数提取异常: ")
                    .append(e.getMessage())
                    .append("\n");
        }
    }

    /**
     * 提取URL参数
     */
    private StringBuilder extractUrlParameters(HttpServletRequest request) {
        StringBuilder urlParams = new StringBuilder();
        try {
            Enumeration<?> paramNames = request.getParameterNames();
            while (paramNames.hasMoreElements()) {
                Object paramName = paramNames.nextElement();
                if (paramName instanceof String) {
                    String name = (String) paramName;
                    String value = request.getParameter(name);
                    if (value != null) {
                        // 处理GET请求中的编码问题
                        if ("GET".equalsIgnoreCase(request.getMethod())) {
                            try {
                                value = new String(value.getBytes(), StandardCharsets.UTF_8);
                            } catch (Exception e) {
                                log.warn("参数编码转换异常: {}", e.getMessage());
                            }
                        }
                        urlParams.append(name)
                                .append("=")
                                .append(value)
                                .append("&");
                    }
                }
            }

            // 移除最后一个&符号
            if (urlParams.length() > 0) {
                urlParams.deleteCharAt(urlParams.length() - 1);
            }
        } catch (Exception e) {
            log.warn("提取URL参数异常: {}", e.getMessage());
        }
        return urlParams;
    }

    /**
     * 处理POST或PUT请求
     */
    private void handlePostOrPutRequest(Object[] args, StringBuilder urlParams,
                                        StringBuilder logBuilder, String requestMethod) {
        // 处理表单参数
        if (urlParams.length() > 0) {
            logBuilder.append(requestMethod)
                    .append("表单 ：")
                    .append(urlParams)
                    .append("\n");
        }

        // 处理请求体参数
        if (args != null && args.length > 0) {
            try {
                // 跳过特殊类型参数
                Object bodyParam = getRequestBodyParam(args);
                if (bodyParam != null) {
                    String paramJson = convertParamToJson(bodyParam);
                    if (paramJson != null && !paramJson.isEmpty()) {
                        logBuilder.append(requestMethod)
                                .append("入参 ：")
                                .append(paramJson)
                                .append("\n");
                    }
                } else if (urlParams.length() == 0) {
                    logBuilder.append(requestMethod)
                            .append("入参 ：无入参\n");
                }
            } catch (Exception e) {
                log.warn("处理请求体参数异常: {}", e.getMessage());
                logBuilder.append(requestMethod)
                        .append("入参 ：参数解析异常\n");
            }
        } else if (urlParams.length() == 0) {
            logBuilder.append(requestMethod)
                    .append("入参 ：无入参\n");
        }
    }

    /**
     * 获取请求体参数
     */
    private Object getRequestBodyParam(Object[] args) {
        if (args == null) {
            return null;
        }

        // 遍历所有参数，找到合适的请求体参数
        for (Object arg : args) {
            if (arg == null) {
                continue;
            }

            // 跳过特殊类型
            if (arg instanceof InputStreamSource || arg instanceof ExtendedServletRequestDataBinder) {
                continue;
            }

            // 返回第一个非特殊类型的参数作为请求体
            return arg;
        }

        return null;
    }

    /**
     * 将参数转换为JSON字符串
     */
    private String convertParamToJson(Object param) {
        try {
            if (param instanceof String) {
                // 尝试解析JSON字符串
                try {
                    new JSONObject(param);
                    return (String) param;
                } catch (Exception e) {
                    // 不是有效的JSON，直接返回字符串
                    return (String) param;
                }
            } else if (param instanceof ExtendedServletRequestDataBinder) {
                return new JSONObject(((ExtendedServletRequestDataBinder) param).getTarget()).toString();
            } else {
                return new JSONObject(param).toString();
            }
        } catch (Exception e) {
            log.warn("参数转JSON异常: {}", e.getMessage());
            return "参数转换异常";
        }
    }

    /**
     * 处理GET或DELETE请求
     */
    private void handleGetOrDeleteRequest(StringBuilder urlParams, StringBuilder logBuilder) {
        if (urlParams.length() > 0) {
            logBuilder.append("GET入参 ：")
                    .append(urlParams)
                    .append("\n");
        } else {
            logBuilder.append("GET入参 ：无入参\n");
        }
    }

    /**
     * 使用 Nginx 进行反向代理，这个方法主要是用来获取远程 IP
     *
     * @param request 请求
     * @return {@link String }
     */
    public String getRemoteIp(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

}
