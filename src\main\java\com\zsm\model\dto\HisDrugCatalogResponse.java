package com.zsm.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * HIS药品目录查询响应
 *
 * <AUTHOR>
 * @date 2025/01/15
 */
@Data
public class HisDrugCatalogResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 返回码
     */
    private Integer returnCode;

    /**
     * 返回消息
     */
    private String returnMsg;

    /**
     * 总页数
     */
    private Integer totalPageCount;

    /**
     * 总记录数
     */
    private Integer totalRecordCount;

    /**
     * 当前页码
     */
    private Integer currentPageNumber;

    /**
     * 药品数据列表
     */
    private List<HisDrugCatalogItem> resultList;

    /**
     * 单个药品信息
     */
    @Data
    public static class HisDrugCatalogItem implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * HIS药品代码
         */
        private String hisDrugCode;

        /**
         * HIS生产企业编码
         */
        private String hisDrugManufacturerCode;

        /**
         * 国家医保编码或药品唯一码
         */
        private String drugUnino;

        /**
         * HIS国家标准名称
         */
        private String hisDrugCountryName;

        /**
         * HIS药品名称
         */
        private String hisDrugName;

        /**
         * 剂型
         */
        private String dosform;

        /**
         * HIS生产企业名称
         */
        private String hisDrugManufacturerName;

        /**
         * 批准文号
         */
        private String aprvno;

        /**
         * HIS药品规格
         */
        private String hisDrugSpec;

        /**
         * HIS药品单位（包装单位）
         */
        private String hisDrugUnit;

        /**
         * 包装数量
         */
        private String hisPac;

        /**
         * 最小包装单位
         */
        private String hisPacUnit;

        /**
         * 价格
         */
        private BigDecimal hisPurchasePrice;
    }
} 