package com.zsm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 药房窗口限定实体类
 * 
 * <AUTHOR>
 * @since 2025-06-02
 */
@Data
@TableName("ysf_sto_win_limit")
@Schema(description = "药房窗口限定")
public class YsfStoWinLimit implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 限定id
     */
    @Schema(description = "限定id")
    @TableId(value = "id_win_limit", type = IdType.AUTO)
    private Long idWinLimit;

    /**
     * 窗口id
     */
    @Schema(description = "窗口id")
    @Size(max = 100, message = "窗口id长度不能超过100个字符")
    @TableField("id_win")
    private String idWin;

    /**
     * 西药权限
     */
    @Schema(description = "西药权限")
    @Size(max = 100, message = "西药权限长度不能超过100个字符")
    @TableField("fg_west")
    private String fgWest;

    /**
     * 成药权限
     */
    @Schema(description = "成药权限")
    @Size(max = 100, message = "成药权限长度不能超过100个字符")
    @TableField("fg_med")
    private String fgMed;

    /**
     * 草药权限
     */
    @Schema(description = "草药权限")
    @Size(max = 100, message = "草药权限长度不能超过100个字符")
    @TableField("fg_herb")
    private String fgHerb;

    /**
     * 疫苗权限
     */
    @Schema(description = "疫苗权限")
    @Size(max = 100, message = "疫苗权限长度不能超过100个字符")
    @TableField("fg_vac")
    private String fgVac;

    /**
     * 耗材权限
     */
    @Schema(description = "耗材权限")
    @Size(max = 100, message = "耗材权限长度不能超过100个字符")
    @TableField("fg_sup")
    private String fgSup;

    /**
     * 民族药权限
     */
    @Schema(description = "民族药权限")
    @Size(max = 100, message = "民族药权限长度不能超过100个字符")
    @TableField("fg_eth")
    private String fgEth;

    /**
     * 院内制剂权限
     */
    @Schema(description = "院内制剂权限")
    @Size(max = 100, message = "院内制剂权限长度不能超过100个字符")
    @TableField("fg_hos")
    private String fgHos;

    /**
     * 机构编号
     */
    @Schema(description = "机构编号")
    @Size(max = 100, message = "机构编号长度不能超过100个字符")
    @TableField("id_org")
    private String idOrg;

    /**
     * 医疗机构编码
     */
    @Schema(description = "医疗机构编码")
    @Size(max = 100, message = "医疗机构编码长度不能超过100个字符")
    @TableField("org_id")
    private String orgId;

    /**
     * 医疗机构名称
     */
    @Schema(description = "医疗机构名称")
    @NotBlank(message = "医疗机构名称不能为空")
    @Size(max = 30, message = "医疗机构名称长度不能超过30个字符")
    @TableField("org_name")
    private String orgName;

    /**
     * 乐观锁
     */
    @Schema(description = "乐观锁")
    @Size(max = 100, message = "乐观锁长度不能超过100个字符")
    @TableField("revision")
    private String revision;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    @Size(max = 100, message = "创建者长度不能超过100个字符")
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    @Size(max = 100, message = "更新者长度不能超过100个字符")
    @TableField("update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @TableField("remark")
    private String remark;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @Schema(description = "删除标志（0代表存在 1代表删除）")
    @Size(max = 100, message = "删除标志（0代表存在 1代表删除）长度不能超过100个字符")
    @TableField("del_flag")
    private String delFlag;
} 
