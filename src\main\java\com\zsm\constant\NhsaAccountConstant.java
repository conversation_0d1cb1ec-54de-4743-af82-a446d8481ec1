package com.zsm.constant;

import com.zsm.model.domain.NhsaAccount;

/**
 * 医疗机构帐号工具类
 *
 * <AUTHOR>
 * @date 2025/07/01
 */
public class NhsaAccountConstant {

    private static final NhsaAccount NHSA_ACCOUNT;

    static {
        NHSA_ACCOUNT = new NhsaAccount();
        NHSA_ACCOUNT.setMedicalCode("H34122200019");
        NHSA_ACCOUNT.setMedicalName("太和县人民医院");
        NHSA_ACCOUNT.setOperatorNo("E0C5E0911C4081C4");
        NHSA_ACCOUNT.setOperatorName("太和县人民医院");
        NHSA_ACCOUNT.setArea("341200");
        // NHSA_ACCOUNT.setBaseUrl("http://10.69.78.158:8086/1.0.0");
        NHSA_ACCOUNT.setBaseUrl("http://192.168.5.13:8086/1.0.0");
    }

    /**
     * 获取医保账户信息
     * 构建医保账户信息对象，用于接口调用
     * 
     * @return 医保账户信息对象
     */
    public static NhsaAccount getNhsaAccount() {
        return NHSA_ACCOUNT;
    }
}