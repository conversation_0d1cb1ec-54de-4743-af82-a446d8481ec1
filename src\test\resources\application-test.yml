spring:
  application:
    name: springboot-mybatisplus-demo-test
  
  # 测试环境使用H2内存数据库
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    
  # H2数据库控制台配置
  h2:
    console:
      enabled: true
      path: /h2-console

# MyBatis Plus 配置
mybatis-plus:
  # 配置 Mapper XML 映射文件路径
  mapper-locations: classpath*:/mapper/**/*.xml
  # 配置 MyBatis 数据返回类型别名（默认别名是类名）
  type-aliases-package: com.zsm.entity
  
  # MyBatis 原生配置
  configuration:
    # 自动驼峰命名规则（camel case）映射
    map-underscore-to-camel-case: true
    # 当查询结果为空时字段返回为 null，不返回空串
    call-setters-on-nulls: true
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    
  # 全局配置
  global-config:
    # 关闭 MyBatis Plus Banner
    banner: false
    # 数据库相关配置
    db-config:
      # 主键类型
      id-type: AUTO
      # 字段策略
      field-strategy: NOT_EMPTY
      # 数据库大写下划线转换
      table-underline: true
      # 逻辑删除配置
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 日志配置
logging:
  level:
    com.zsm.mapper: debug
    org.springframework.jdbc: debug
  pattern:
    console: '%d{HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'

# 杭创接口配置 - 测试环境
hangchuang:
  # 服务器地址配置
  server: 192.168.5.13:720
  # HIS接口服务器地址配置
  his-server: http://192.168.5.13:605 