package com.zsm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zsm.entity.Nhsa3505;
import com.zsm.model.ApiResult;
import com.zsm.model.vo.OutpatientPrescriptionResponseVo;
import com.zsm.model.vo.InPatientDispenseDetailBindScatteredVo;
import com.zsm.model.vo.SaasUserInfoResponse;

import java.util.List;

/**
 * <p>
 * 3505销售记录报文表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-02
 */
public interface Nhsa3505Service extends IService<Nhsa3505> {

    /**
     * 保存3505销售记录-异步
     *
     * @param dataList 数据列表
     */
    void save3505Async(List<OutpatientPrescriptionResponseVo.PrescriptionItem> dataList);

    /**
     * 异步保存住院发药数据到nhsa_3505表
     *
     * @param inpatientList 住院发药数据列表
     */
    void saveInpatientDataToNhsa3505Async(List<InPatientDispenseDetailBindScatteredVo> inpatientList);

    /**
     * 异步保存住院发药数据到nhsa_3505表（带用户上下文）
     *
     * @param inpatientList 住院发药数据列表
     * @param userInfo 用户信息，用于设置到异步线程上下文中
     */
    void saveInpatientDataToNhsa3505AsyncWithContext(List<InPatientDispenseDetailBindScatteredVo> inpatientList, SaasUserInfoResponse userInfo);

    /**
     * 更新药品关联的追溯码
     *
     * @param outPresdetailid 处方明细序号
     * @param drugtracinfo    追溯码
     * @param userName        用户名
     * @param orgId           组织id
     * @param orgName         组织名称
     */
    void updateDrugTraceabilityInfo(String outPresdetailid, String drugtracinfo, String userName, String orgId, String orgName);

    /**
     * 更新药物可追溯性信息
     *
     * @param fixmedinsBchno fixmedins bchno
     * @param drugtracinfo   追溯码
     */
    void updateDrugTraceabilityInfo(String fixmedinsBchno, String drugtracinfo);

    /**
     * 上传3505报文数据到医保平台
     */
    void uploadDataToPlatform();

    /**
     * 同步发药时间
     */
    void syncDispenseTime();


    /**
     * 手动上传数据到两定接口平台（支持按条件筛选）
     *
     * @param cfmxxh 处方明细序号，可选
     * @param cfxh   处方序号，可选
     * @return 操作结果
     */
    ApiResult<String> manualUploadDataToPlatform(String cfmxxh, String cfxh);

    /**
     * 同步太和人医发药时间
     */
    void syncTaiHeDispenseTime();

    /**
     * 上传3505数据到最终平台
     *
     * @param dataList 数据列表
     * @param isManual 是否为手动上传
     * @return 操作结果
     */
    ApiResult<String> processUpload(List<Nhsa3505> dataList, boolean isManual);
}
