package com.zsm.model.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class PrescriptionTrackingLog {

    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private ZsmView3505 zsmView;

    /**
     * 日志ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    private String medicalCode;

    private String medicalName;

    private String outPresId;

    private String prescriptionDetailId;

    private String dispenseId;

    /**
     * 患者ID
     */
    private String patId;


    /**
     * 患者主卡号
     */
    private String patCardNo;

    /**
     * 患者姓名
     */
    private String patName;

    /**
     * 患者手机号
     */
    private String telephone;

    /**
     * 业务类别：1：门诊，2：住院
     */
    private Integer sortType;

    /**
     * 处方编号
     */
    private String presCode;


    /**
     * 药品名称
     */
    private String drugName;

    /**
     * 药品编码
     */
    private String drugCode;

    /**
     * 药品规格
     */
    private String spec;

    /**
     * 药品生产厂家
     */
    private String prodentpName;

    /**
     * 药品数量
     */
    private Integer quantity;

    /**
     * 包装药品数量
     */
    private Integer pacQuantity;

    /**
     * 匹配状态: 1-未采集, 2-部分采集, 3-采集完成
     */
    private Integer matchStatus;

    /**
     * 已匹配的追溯码，多个追溯码使用逗号分隔
     */
    private String matchedCodes;

    /**
     * 未匹配的追溯码，多个追溯码使用逗号分隔
     */
    private String unmatchedCodes;

    /**
     * 拆零标识：0-否，1-是
     */
    private Integer trdnFlag;

    /**
     * 国家医保编码
     */
    private String drugUnino;

    /**
     * 追溯码包装层级:1-小包装 2-中包装 3-大包装
     */
    private String tracCodgPacHery;

    private Integer hisMatchFlag;


    private Long ymfUserId;
    private String ymfUserName;
    private String ymfNickName;

    private String createBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    private String updateBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private String remark;

}
