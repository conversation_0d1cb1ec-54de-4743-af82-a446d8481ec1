<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <!-- 定义日志文件的存储地址，勿在 LogBack 的配置中使用相对路径 -->
    <!-- 使用 Spring Boot 的标准日志路径配置，优先使用 application.yml 中的 logging.file.path -->
    <property name="LOG_PATH" value="${logging.file.path:-/opt/taiherenyi/logs}"/>
    <property name="APP_NAME" value="${spring.application.name:-taiHeRenYi}"/>
    
    <!-- 彩色日志依赖的渲染类 -->
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
    <conversionRule conversionWord="wex" converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="wEx" converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>

    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!-- 开发环境彩色日志格式 -->
            <springProfile name="dev">
                <pattern>%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr([%X{traceId:-}]){yellow} %clr([%thread]){orange} %clr(%-5level){highlight} %clr(%logger{50}){cyan}:%L - %msg%n</pattern>
            </springProfile>
            <!-- 测试和生产环境普通格式 -->
            <springProfile name="!dev">
                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%X{traceId:-}] [%thread] %-5level %logger{50}:%L - %msg%n</pattern>
            </springProfile>
            <charset>UTF-8</charset>
        </encoder>
        <!-- 控制台日志级别过滤器 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <springProfile name="dev">
                <level>DEBUG</level>
            </springProfile>
            <springProfile name="test">
                <level>INFO</level>
            </springProfile>
            <springProfile name="prod">
                <level>WARN</level>
            </springProfile>
        </filter>
    </appender>

    <!-- INFO级别日志文件输出 -->
    <appender name="INFO_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/${APP_NAME}-info.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/history/%d{yyyy-MM-dd}/${APP_NAME}-info-%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <!-- 单个文件最大大小 -->
            <maxFileSize>20MB</maxFileSize>
            <!-- 保留天数 -->
            <springProfile name="dev">
                <maxHistory>7</maxHistory>
                <totalSizeCap>1GB</totalSizeCap>
            </springProfile>
            <springProfile name="test">
                <maxHistory>30</maxHistory>
                <totalSizeCap>5GB</totalSizeCap>
            </springProfile>
            <springProfile name="prod">
                <maxHistory>90</maxHistory>
                <totalSizeCap>20GB</totalSizeCap>
            </springProfile>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%X{traceId:-}] [%thread] %-5level %logger{50}:%L - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- 只记录INFO及以上级别的日志 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>NEUTRAL</onMismatch>
        </filter>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
    </appender>

    <!-- WARN级别日志文件输出 -->
    <appender name="WARN_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/${APP_NAME}-warn.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/history/%d{yyyy-MM-dd}/${APP_NAME}-warn-%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>20MB</maxFileSize>
            <springProfile name="dev">
                <maxHistory>7</maxHistory>
                <totalSizeCap>500MB</totalSizeCap>
            </springProfile>
            <springProfile name="test">
                <maxHistory>30</maxHistory>
                <totalSizeCap>2GB</totalSizeCap>
            </springProfile>
            <springProfile name="prod">
                <maxHistory>90</maxHistory>
                <totalSizeCap>10GB</totalSizeCap>
            </springProfile>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%X{traceId:-}] [%thread] %-5level %logger{50}:%L - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- 只记录WARN级别的日志 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- ERROR级别日志文件输出 -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/${APP_NAME}-error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/history/%d{yyyy-MM-dd}/${APP_NAME}-error-%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>20MB</maxFileSize>
            <springProfile name="dev">
                <maxHistory>7</maxHistory>
                <totalSizeCap>500MB</totalSizeCap>
            </springProfile>
            <springProfile name="test">
                <maxHistory>30</maxHistory>
                <totalSizeCap>2GB</totalSizeCap>
            </springProfile>
            <springProfile name="prod">
                <maxHistory>90</maxHistory>
                <totalSizeCap>10GB</totalSizeCap>
            </springProfile>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%X{traceId:-}] [%thread] %-5level %logger{50}:%L - %msg%wEx%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- 只记录ERROR级别的日志 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- SQL日志文件输出（仅开发和测试环境） -->
    <springProfile name="dev,test">
        <appender name="SQL_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>${LOG_PATH}/${APP_NAME}-sql.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
                <fileNamePattern>${LOG_PATH}/history/%d{yyyy-MM-dd}/${APP_NAME}-sql-%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
                <maxFileSize>20MB</maxFileSize>
                <maxHistory>7</maxHistory>
                <totalSizeCap>500MB</totalSizeCap>
            </rollingPolicy>
            <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%X{traceId:-}] [%thread] %-5level %logger{50}:%L - %msg%n</pattern>
                <charset>UTF-8</charset>
            </encoder>
        </appender>
    </springProfile>

    <!-- 异步INFO日志 -->
    <appender name="ASYNC_INFO" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 队列大小 -->
        <queueSize>1024</queueSize>
        <!-- 丢弃阈值，当队列剩余容量小于这个值时，会丢弃TRACE、DEBUG、INFO级别的日志 -->
        <discardingThreshold>0</discardingThreshold>
        <!-- 是否包含调用者信息 -->
        <includeCallerData>true</includeCallerData>
        <!-- 是否阻塞 -->
        <neverBlock>false</neverBlock>
        <!-- 最大刷新时间 -->
        <maxFlushTime>2000</maxFlushTime>
        <appender-ref ref="INFO_FILE"/>
    </appender>

    <!-- 异步WARN日志 -->
    <appender name="ASYNC_WARN" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>512</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <includeCallerData>true</includeCallerData>
        <neverBlock>false</neverBlock>
        <maxFlushTime>2000</maxFlushTime>
        <appender-ref ref="WARN_FILE"/>
    </appender>

    <!-- 异步ERROR日志 -->
    <appender name="ASYNC_ERROR" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>512</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <includeCallerData>true</includeCallerData>
        <neverBlock>false</neverBlock>
        <maxFlushTime>2000</maxFlushTime>
        <appender-ref ref="ERROR_FILE"/>
    </appender>

    <!-- 开发环境配置 -->
    <springProfile name="dev">
        <!-- SQL日志配置 -->
        <logger name="com.zsm.mapper" level="DEBUG" additivity="false">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="SQL_FILE"/>
        </logger>
        
        <!-- 数据源日志 -->
        <logger name="com.baomidou.dynamic.datasource" level="DEBUG" additivity="false">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="SQL_FILE"/>
        </logger>
        
        <!-- Druid SQL日志 -->
        <logger name="druid.sql.Statement" level="DEBUG" additivity="false">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="SQL_FILE"/>
        </logger>
        
        <!-- 业务日志 -->
        <logger name="com.zsm" level="DEBUG" additivity="false">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="ASYNC_INFO"/>
            <appender-ref ref="ASYNC_WARN"/>
            <appender-ref ref="ASYNC_ERROR"/>
        </logger>
        
        <!-- 根日志配置 -->
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="ASYNC_INFO"/>
            <appender-ref ref="ASYNC_WARN"/>
            <appender-ref ref="ASYNC_ERROR"/>
        </root>
    </springProfile>

    <!-- 测试环境配置 -->
    <springProfile name="test">
        <!-- SQL日志配置 -->
        <logger name="com.zsm.mapper" level="DEBUG" additivity="false">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="SQL_FILE"/>
        </logger>
        
        <!-- 数据源日志 -->
        <logger name="com.baomidou.dynamic.datasource" level="DEBUG" additivity="false">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="SQL_FILE"/>
        </logger>
        
        <!-- Druid SQL日志 -->
        <logger name="druid.sql.Statement" level="INFO" additivity="false">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="SQL_FILE"/>
        </logger>
        
        <!-- 业务日志 -->
        <logger name="com.zsm" level="INFO" additivity="false">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="ASYNC_INFO"/>
            <appender-ref ref="ASYNC_WARN"/>
            <appender-ref ref="ASYNC_ERROR"/>
        </logger>
        
        <!-- 根日志配置 -->
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="ASYNC_INFO"/>
            <appender-ref ref="ASYNC_WARN"/>
            <appender-ref ref="ASYNC_ERROR"/>
        </root>
    </springProfile>

    <!-- 生产环境配置 -->
    <springProfile name="prod">
        <!-- SQL日志配置 - 生产环境只记录WARN及以上级别 -->
        <logger name="com.zsm.mapper" level="WARN" additivity="false">
            <appender-ref ref="ASYNC_WARN"/>
            <appender-ref ref="ASYNC_ERROR"/>
        </logger>
        
        <!-- 数据源日志 -->
        <logger name="com.baomidou.dynamic.datasource" level="WARN" additivity="false">
            <appender-ref ref="ASYNC_WARN"/>
            <appender-ref ref="ASYNC_ERROR"/>
        </logger>
        
        <!-- Druid SQL日志 -->
        <logger name="druid.sql.Statement" level="WARN" additivity="false">
            <appender-ref ref="ASYNC_WARN"/>
            <appender-ref ref="ASYNC_ERROR"/>
        </logger>
        
        <!-- 业务日志 -->
        <logger name="com.zsm" level="INFO" additivity="false">
            <appender-ref ref="ASYNC_INFO"/>
            <appender-ref ref="ASYNC_WARN"/>
            <appender-ref ref="ASYNC_ERROR"/>
        </logger>
        
        <!-- Spring框架日志 -->
        <logger name="org.springframework" level="WARN" additivity="false">
            <appender-ref ref="ASYNC_WARN"/>
            <appender-ref ref="ASYNC_ERROR"/>
        </logger>
        
        <!-- Hibernate日志 -->
        <logger name="org.hibernate" level="WARN" additivity="false">
            <appender-ref ref="ASYNC_WARN"/>
            <appender-ref ref="ASYNC_ERROR"/>
        </logger>
        
        <!-- 根日志配置 -->
        <root level="WARN">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="ASYNC_INFO"/>
            <appender-ref ref="ASYNC_WARN"/>
            <appender-ref ref="ASYNC_ERROR"/>
        </root>
    </springProfile>

    <!-- 关闭一些不必要的日志 -->
    <logger name="org.apache.catalina.startup.DigesterFactory" level="ERROR"/>
    <logger name="org.apache.catalina.util.LifecycleBase" level="ERROR"/>
    <logger name="org.apache.coyote.http11.Http11NioProtocol" level="WARN"/>
    <logger name="org.apache.sshd.common.util.SecurityUtils" level="WARN"/>
    <logger name="org.eclipse.jetty.util.component.AbstractLifeCycle" level="ERROR"/>
    <logger name="org.hibernate.validator.internal.util.Version" level="WARN"/>
    <logger name="org.springframework.boot.actuate.endpoint.jmx" level="WARN"/>
    <logger name="com.zaxxer.hikari" level="WARN"/>
    
</configuration>
