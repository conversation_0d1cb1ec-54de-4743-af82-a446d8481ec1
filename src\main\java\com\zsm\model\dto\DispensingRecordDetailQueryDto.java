package com.zsm.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 发药单明细查询请求参数
 *
 * <AUTHOR>
 * @date 2025/06/03
 */
@Data
@Schema(description = "发药单明细查询请求参数")
public class DispensingRecordDetailQueryDto {

    @Schema(description = "处方序号")
    private String cfxh;

    @Schema(description = "发药单ID (id_dps)")
    private Long idDps;

    @Schema(description = "患者姓名")
    private String patientName;

    @Schema(description = "药品名称或编码")
    private String drugNameOrCode;

    @Schema(description = "是否已采集", example = "0")
    private String isCollected;

    @Schema(description = "页码", example = "1")
    private Integer pageNum;

    @Schema(description = "每页数量", example = "10")
    private Integer pageSize;
} 