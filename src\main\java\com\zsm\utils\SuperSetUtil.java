package com.zsm.utils;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.zsm.entity.Nhsa3505CntCount;
import com.zsm.entity.Nhsa3505YmfUserCount;
import com.zsm.model.ApiResult;
import com.zsm.model.dto.Nhsa3505MdtrtSetlTypeCount;
import com.zsm.model.dto.NhsaHospitalUploadCount;
import com.zsm.model.dto.PrescriptionTrackingLogListRequest;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 超级平台工具类
 * <AUTHOR>
 * @date 2025/6/5 下午5:14
 */
@Slf4j
public class SuperSetUtil {

    private final static String UPLOAD_NHSA_HOSPITAL_UPLOAD_COUNT_LIST = "/ymf/hospitalInterface/uploadNhsaHospitalUploadCountList";
    private final static String UPLOAD_NHSA_3505_CNT_COUNT_LIST = "/ymf/hospitalInterface/uploadNhsa3505CntCountList";
    private final static String UPLOAD_PRESCRIPTION_TRACKING_LOG_LIST = "/ymf/hospitalInterface/uploadPrescriptionTrackingLogList";
    private final static String GET_CODE_READER_LIST = "/ymf/hospitalInterface/getCodeReaderList";
    private final static String UPLOAD_NHSA_3505_YMF_USER_COUNT_LIST = "/ymf/hospitalInterface/uploadNhsa3505YmfUserCountList";
    private final static String UPLOAD_NHSA_3505_MDTRT_SETL_TYPE_LIST = "/ymf/hospitalInterface/uploadNhsa3505MdtrtSetlTypeList";
    private final static Integer TIME_OUT = 60000;
    /**
     * TODO:医秒付接口基础地址,如果院内的网络环境受限,那么修改为可以访问的接口地址
     */
    // private final static String YMF_BASE_URL = "http://127.0.0.1:30018";
    private final static String YMF_BASE_URL = "http://*************:30800";

    /**
     * 通用HTTP POST请求方法
     * @param endpoint 接口端点
     * @param request 请求数据
     * @param headers 请求头（可选）
     * @return 响应结果
     */
    private static String executePostRequest(String endpoint, Object request, Map<String, String> headers) {
        String logPrefix = endpoint.substring(endpoint.lastIndexOf("/") + 1);
        String url = YMF_BASE_URL + endpoint;
        
        try {
            log.info("{}请求地址：{}", logPrefix, url);
            if (headers != null && !headers.isEmpty()) {
                log.info("{}请求头：{}", logPrefix, JSONUtil.toJsonStr(headers));
            }
            log.info("{}请求数据：{}", logPrefix, JSONUtil.toJsonStr(request));
            
            HttpRequest httpRequest = HttpRequest.post(url)
                    .timeout(TIME_OUT)
                    .body(JSONUtil.toJsonStr(request));
            
            if (headers != null && !headers.isEmpty()) {
                httpRequest.headerMap(headers, false);
            }
            
            HttpResponse response = httpRequest.execute();
            String body = response.body();
            
            log.info("{}响应数据：{}", logPrefix, body);
            return body;
            
        } catch (Exception e) {
            log.error("{}请求异常：", logPrefix, e);
            throw new RuntimeException("调用医秒付接口异常：" + e.getMessage(), e);
        }
    }

    /**
     * 通用解析ApiResult响应的方法
     * @param responseBody 响应体
     * @return ApiResult对象
     */
    private static ApiResult parseApiResult(String responseBody) {
        try {
            return JSONUtil.toBean(responseBody, ApiResult.class);
        } catch (Exception e) {
            log.error("解析ApiResult响应异常：", e);
            throw new RuntimeException("解析响应数据异常：" + e.getMessage(), e);
        }
    }

    public static ApiResult uploadPrescriptionTrackingLogList(PrescriptionTrackingLogListRequest request) {
        String responseBody = executePostRequest(UPLOAD_PRESCRIPTION_TRACKING_LOG_LIST, request, null);
        return parseApiResult(responseBody);
    }

    public static ApiResult uploadNhsaHospitalUploadCountList(List<NhsaHospitalUploadCount> request) {
        String responseBody = executePostRequest(UPLOAD_NHSA_HOSPITAL_UPLOAD_COUNT_LIST, request, null);
        return parseApiResult(responseBody);
    }

    public static ApiResult uploadNhsa3505CntCountList(List<Nhsa3505CntCount> request) {
        String responseBody = executePostRequest(UPLOAD_NHSA_3505_CNT_COUNT_LIST, request, null);
        return parseApiResult(responseBody);
    }

    public static JSONObject getCodeReaderList(String medicalCode, JSONObject request) {
        Map<String, String> headers = new HashMap<>();
        headers.put("medicalCode", medicalCode);
        
        String responseBody = executePostRequest(GET_CODE_READER_LIST, request, headers);
        
        try {
            return JSONUtil.parseObj(responseBody);
        } catch (Exception e) {
            log.error("解析JSONObject响应异常：", e);
            throw new RuntimeException("解析响应数据异常：" + e.getMessage(), e);
        }
    }

    public static ApiResult uploadNhsa3505YmfUserCountList(List<Nhsa3505YmfUserCount> request) {
        String responseBody = executePostRequest(UPLOAD_NHSA_3505_YMF_USER_COUNT_LIST, request, null);
        return parseApiResult(responseBody);
    }

    public static ApiResult uploadNhsa3505MdtrtSetlTypeList(List<Nhsa3505MdtrtSetlTypeCount> request) {
        String responseBody = executePostRequest(UPLOAD_NHSA_3505_MDTRT_SETL_TYPE_LIST, request, null);
        return parseApiResult(responseBody);
    }
}
