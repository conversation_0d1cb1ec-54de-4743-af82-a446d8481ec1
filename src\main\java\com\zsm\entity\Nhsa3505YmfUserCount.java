package com.zsm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 医秒付用户维度销售数据3505采集数量实体类
 * 
 * <AUTHOR>
 * @since 2025-06-05
 */
@Data
@TableName("nhsa3505_ymf_user_count")
@Schema(description = "医秒付用户维度销售数据3505采集数量")
public class Nhsa3505YmfUserCount implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 医疗机构编码
     */
    @Schema(description = "医疗机构编码")
    @Size(max = 100, message = "医疗机构编码长度不能超过100个字符")
    @TableField("medical_code")
    private String medicalCode;

    /**
     * 医疗机构
     */
    @Schema(description = "医疗机构")
    @NotBlank(message = "医疗机构不能为空")
    @Size(max = 30, message = "医疗机构长度不能超过30个字符")
    @TableField("medical_name")
    private String medicalName;

    /**
     * 统计日期
     */
    @Schema(description = "统计日期")
    @Size(max = 100, message = "统计日期长度不能超过100个字符")
    @TableField("count_date")
    private String countDate;

    /**
     * cnt总数量
     */
    @Schema(description = "cnt总数量")
    @TableField("total_count")
    private Integer totalCount;

    /**
     * 追溯码数量
     */
    @Schema(description = "追溯码数量")
    @TableField("trace_code_count")
    private Integer traceCodeCount;

    /**
     * 所有数据cnt数量,以及没有追溯码的
     */
    @Schema(description = "所有数据cnt数量,以及没有追溯码的")
    @TableField("all_cnt_count")
    private Integer allCntCount;

    /**
     * 所有数据条数
     */
    @Schema(description = "所有数据条数")
    @TableField("all_data_number")
    private Integer allDataNumber;

    /**
     * 所有含追溯码的数据条数
     */
    @Schema(description = "所有含追溯码的数据条数")
    @TableField("trace_code_data_number")
    private Integer traceCodeDataNumber;

    /**
     * ymf用户ID
     */
    @Schema(description = "ymf用户ID")
    @TableField("ymf_user_id")
    private Long ymfUserId;

    /**
     * ymf用户账号
     */
    @Schema(description = "ymf用户账号")
    @NotBlank(message = "ymf用户账号不能为空")
    @Size(max = 30, message = "ymf用户账号长度不能超过30个字符")
    @TableField("ymf_user_name")
    private String ymfUserName;

    /**
     * ymf用户昵称
     */
    @Schema(description = "ymf用户昵称")
    @NotBlank(message = "ymf用户昵称不能为空")
    @Size(max = 30, message = "ymf用户昵称长度不能超过30个字符")
    @TableField("ymf_nick_name")
    private String ymfNickName;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    @Size(max = 100, message = "创建者长度不能超过100个字符")
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    @Size(max = 100, message = "更新者长度不能超过100个字符")
    @TableField("update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @TableField("remark")
    private String remark;

    /**
     * 删除标志（0代表正常,1代表删除）
     */
    @Schema(description = "删除标志（0代表正常,1代表删除）")
    @Size(max = 100, message = "删除标志（0代表正常,1代表删除）长度不能超过100个字符")
    @TableField("delete_flag")
    private String deleteFlag;
} 
