package com.zsm.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 太和人医门诊处方明细VO
 *
 * <AUTHOR>
 * @date 2025-01-12
 */
@Data
@Schema(description = "门诊处方明细请求数据")
public class OutpatientPrescriptionVo {

    @Schema(description = "药品目录编码")
    private String med_list_codg;

    @Schema(description = "定点医疗机构目录ID")
    private String fixmedins_hilist_id;

    @Schema(description = "定点医疗机构目录名称")
    private String fixmedins_hilist_name;

    @Schema(description = "定点医疗机构批次号")
    private String fixmedins_bchno;

    @Schema(description = "开方医师姓名")
    private String prsc_dr_name;

    @Schema(description = "药师姓名")
    private String phar_name;

    @Schema(description = "药师执业证书编号")
    private String phar_prac_cert_no;

    @Schema(description = "就诊序列号")
    private String mdtrt_sn;

    @Schema(description = "人员姓名")
    private String psn_name;

    @Schema(description = "生产批号")
    private String manu_lotnum;

    @Schema(description = "有效期截止")
    private String expy_end;

    @Schema(description = "处方标志")
    private String rx_flag;

    @Schema(description = "拆零标志")
    private String trdn_flag;

    @Schema(description = "处方号")
    private String rxno;

    @Schema(description = "处方流转标志")
    private String rx_circ_flag;

    @Schema(description = "零售单据号")
    private String rtal_docno;

    @Schema(description = "库存出库单号")
    private String stoout_no;

    @Schema(description = "批次号")
    private String bchno;

    @Schema(description = "售退数量")
    private Integer sel_retn_cnt;

    @Schema(description = "最小售退数量")
    private Integer min_sel_retn_cnt;

    @Schema(description = "售退单位")
    private String sel_retn_unit;

    @Schema(description = "HIS剂量单位")
    private String his_dos_unit;

    @Schema(description = "HIS包装单位")
    private String his_pac_unit;

    @Schema(description = "售退时间")
    private String sel_retn_time;

    @Schema(description = "售退经办人姓名")
    private String sel_retn_opter_name;

    @Schema(description = "就诊结算类型")
    private String mdtrt_setl_type;

    @Schema(description = "规格")
    private String spec;

    @Schema(description = "生产企业名称")
    private String prodentp_name;

    @Schema(description = "处方序号")
    private String cfxh;

    @Schema(description = "处方明细序号")
    private String cfmxxh;

    @Schema(description = "售价号")
    private String sjh;

    @Schema(description = "患者ID")
    private String patient_id;

    @Schema(description = "HIS剂量换算比例")
    private String his_con_ratio;

    @Schema(description = "发送标志")
    private String send_flag;

    @Schema(description = "发送时间")
    private String send_time;

    @Schema(description = "科室ID")
    private String dept_id;

    @Schema(description = "科室名称")
    private String dept_name;

    @Schema(description = "窗口")
    private String window;
    // saas拆零接口字段
    @Schema(description = "药品编码")
    private String drugCode;

    @Schema(description = "药品追溯码列表")
    private List<String> drugTracCodgs;

    @Schema(description = "配发数量")
    private String dispCnt;

    @Schema(description = "拆零池剩余数量")
    private BigDecimal currNum;
} 