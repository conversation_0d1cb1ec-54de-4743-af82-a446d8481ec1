package com.zsm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 3505销售记录报文表实体类
 * 
 * <AUTHOR>
 * @since 2025-06-02
 */
@Data
@TableName("nhsa_3505")
@Schema(description = "3505销售记录报文表")
public class Nhsa3505 implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 医疗机构编码
     */
    @Schema(description = "医疗机构编码")
    @Size(max = 100, message = "医疗机构编码长度不能超过100个字符")
    @TableField("medical_code")
    private String medicalCode;

    /**
     * 医疗机构
     */
    @Schema(description = "医疗机构")
    @NotBlank(message = "医疗机构不能为空")
    @Size(max = 30, message = "医疗机构长度不能超过30个字符")
    @TableField("medical_name")
    private String medicalName;

    /**
     * 医疗目录编码
     */
    @Schema(description = "医疗目录编码")
    @Size(max = 100, message = "医疗目录编码长度不能超过100个字符")
    @TableField("med_list_codg")
    private String medListCodg;

    /**
     * 定点医药机构目录编号
     */
    @Schema(description = "定点医药机构目录编号")
    @Size(max = 100, message = "定点医药机构目录编号长度不能超过100个字符")
    @TableField("fixmedins_hilist_id")
    private String fixmedinsHilistId;

    /**
     * 定点医药计够目录名称
     */
    @Schema(description = "定点医药计够目录名称")
    @NotBlank(message = "定点医药计够目录名称不能为空")
    @Size(max = 30, message = "定点医药计够目录名称长度不能超过30个字符")
    @TableField("fixmedins_hilist_name")
    private String fixmedinsHilistName;

    /**
     * 定点医药机构批次流水号
     */
    @Schema(description = "定点医药机构批次流水号")
    @Size(max = 100, message = "定点医药机构批次流水号长度不能超过100个字符")
    @TableField("fixmedins_bchno")
    private String fixmedinsBchno;

    /**
     * 开方医师证件类型
     */
    @Schema(description = "开方医师证件类型")
    @Size(max = 100, message = "开方医师证件类型长度不能超过100个字符")
    @TableField("prsc_dr_cert_type")
    private String prscDrCertType;

    /**
     * 开方医师证件号码
     */
    @Schema(description = "开方医师证件号码")
    @Size(max = 100, message = "开方医师证件号码长度不能超过100个字符")
    @TableField("prsc_dr_certno")
    private String prscDrCertno;

    /**
     * 开方医师证件姓名
     */
    @Schema(description = "开方医师证件姓名")
    @NotBlank(message = "开方医师证件姓名不能为空")
    @Size(max = 30, message = "开方医师证件姓名长度不能超过30个字符")
    @TableField("prsc_dr_name")
    private String prscDrName;

    /**
     * 药师证件类型
     */
    @Schema(description = "药师证件类型")
    @Size(max = 100, message = "药师证件类型长度不能超过100个字符")
    @TableField("phar_cert_type")
    private String pharCertType;

    /**
     * 药师证件号码
     */
    @Schema(description = "药师证件号码")
    @Size(max = 100, message = "药师证件号码长度不能超过100个字符")
    @TableField("phar_certno")
    private String pharCertno;

    /**
     * 药师姓名
     */
    @Schema(description = "药师姓名")
    @NotBlank(message = "药师姓名不能为空")
    @Size(max = 30, message = "药师姓名长度不能超过30个字符")
    @TableField("phar_name")
    private String pharName;

    /**
     * 药师执业资格证号
     */
    @Schema(description = "药师执业资格证号")
    @Size(max = 100, message = "药师执业资格证号长度不能超过100个字符")
    @TableField("phar_prac_cert_no")
    private String pharPracCertNo;

    /**
     * 医保费用结算类型
     */
    @Schema(description = "医保费用结算类型")
    @Size(max = 100, message = "医保费用结算类型长度不能超过100个字符")
    @TableField("hi_feesetl_type")
    private String hiFeesetlType;

    /**
     * 结算ID
     */
    @Schema(description = "结算ID")
    @Size(max = 100, message = "结算ID长度不能超过100个字符")
    @TableField("setl_id")
    private String setlId;

    /**
     * 就医流水号
     */
    @Schema(description = "就医流水号")
    @Size(max = 100, message = "就医流水号长度不能超过100个字符")
    @TableField("mdtrt_sn")
    private String mdtrtSn;

    /**
     * 人员编号
     */
    @Schema(description = "人员编号")
    @Size(max = 100, message = "人员编号长度不能超过100个字符")
    @TableField("psn_no")
    private String psnNo;

    /**
     * 人员证件类型
     */
    @Schema(description = "人员证件类型")
    @Size(max = 100, message = "人员证件类型长度不能超过100个字符")
    @TableField("psn_cert_type")
    private String psnCertType;

    /**
     * 证件号码
     */
    @Schema(description = "证件号码")
    @Size(max = 100, message = "证件号码长度不能超过100个字符")
    @TableField("certno")
    private String certno;

    /**
     * 人员姓名
     */
    @Schema(description = "人员姓名")
    @NotBlank(message = "人员姓名不能为空")
    @Size(max = 30, message = "人员姓名长度不能超过30个字符")
    @TableField("psn_name")
    private String psnName;

    /**
     * 生产批号
     */
    @Schema(description = "生产批号")
    @Size(max = 100, message = "生产批号长度不能超过100个字符")
    @TableField("manu_lotnum")
    private String manuLotnum;

    /**
     * 生产日期
     */
    @Schema(description = "生产日期")
    @TableField("manu_date")
    private LocalDate manuDate;

    /**
     * 有效期止
     */
    @Schema(description = "有效期止")
    @TableField("expy_end")
    private LocalDate expyEnd;

    /**
     * 处方药标志
     */
    @Schema(description = "处方药标志")
    @Size(max = 100, message = "处方药标志长度不能超过100个字符")
    @TableField("rx_flag")
    private String rxFlag;

    /**
     * 拆零标识
     */
    @Schema(description = "拆零标识")
    @Size(max = 100, message = "拆零标识长度不能超过100个字符")
    @TableField("trdn_flag")
    private String trdnFlag;

    /**
     * 最终成交单价
     */
    @Schema(description = "最终成交单价")
    @TableField("finl_trns_pric")
    private BigDecimal finlTrnsPric;

    /**
     * 处方号
     */
    @Schema(description = "处方号")
    @Size(max = 100, message = "处方号长度不能超过100个字符")
    @TableField("rxno")
    private String rxno;

    /**
     * 外购处方标志
     */
    @Schema(description = "外购处方标志")
    @Size(max = 100, message = "外购处方标志长度不能超过100个字符")
    @TableField("rx_circ_flag")
    private String rxCircFlag;

    /**
     * 零售单据号
     */
    @Schema(description = "零售单据号")
    @Size(max = 100, message = "零售单据号长度不能超过100个字符")
    @TableField("rtal_docno")
    private String rtalDocno;

    /**
     * 销售出库单据号
     */
    @Schema(description = "销售出库单据号")
    @Size(max = 100, message = "销售出库单据号长度不能超过100个字符")
    @TableField("stoout_no")
    private String stooutNo;

    /**
     * 批次号
     */
    @Schema(description = "批次号")
    @Size(max = 100, message = "批次号长度不能超过100个字符")
    @TableField("bchno")
    private String bchno;

    /**
     * 药品条形码
     */
    @Schema(description = "药品条形码")
    @Size(max = 100, message = "药品条形码长度不能超过100个字符")
    @TableField("drug_prod_barc")
    private String drugProdBarc;

    /**
     * 货架位
     */
    @Schema(description = "货架位")
    @Size(max = 100, message = "货架位长度不能超过100个字符")
    @TableField("shelf_posi")
    private String shelfPosi;

    /**
     * 销售/退货数量
     */
    @Schema(description = "销售/退货数量")
    @TableField("sel_retn_cnt")
    private BigDecimal selRetnCnt;

    /**
     * 销售/退货时间
     */
    @Schema(description = "销售/退货时间")
    @TableField("sel_retn_time")
    private LocalDateTime selRetnTime;

    /**
     * 销售/退货经办人姓名
     */
    @Schema(description = "销售/退货经办人姓名")
    @NotBlank(message = "销售/退货经办人姓名不能为空")
    @Size(max = 30, message = "销售/退货经办人姓名长度不能超过30个字符")
    @TableField("sel_retn_opter_name")
    private String selRetnOpterName;

    /**
     * 就诊结算类型
     */
    @Schema(description = "就诊结算类型")
    @Size(max = 100, message = "就诊结算类型长度不能超过100个字符")
    @TableField("mdtrt_setl_type")
    private String mdtrtSetlType;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Size(max = 100, message = "备注长度不能超过100个字符")
    @TableField("memo")
    private String memo;

    /**
     * 溯源码节点信息
     */
    @Schema(description = "溯源码节点信息")
    @Size(max = 100, message = "溯源码节点信息长度不能超过100个字符")
    @TableField("drug_trac_info")
    private String drugTracInfo;

    /**
     * 第三方业务主键
     */
    @Schema(description = "第三方业务主键")
    @Size(max = 100, message = "第三方业务主键长度不能超过100个字符")
    @TableField("third_id")
    private String thirdId;

    /**
     * 异常内容
     */
    @Schema(description = "异常内容")
    @Size(max = 100, message = "异常内容长度不能超过100个字符")
    @TableField("exp_content")
    private String expContent;

    /**
     * HIS药品ID
     */
    @Schema(description = "HIS药品ID")
    @Size(max = 100, message = "HIS药品ID长度不能超过100个字符")
    @TableField("his_drug_id")
    private String hisDrugId;

    /**
     * HIS企业编码
     */
    @Schema(description = "HIS企业编码")
    @Size(max = 100, message = "HIS企业编码长度不能超过100个字符")
    @TableField("his_entp_code")
    private String hisEntpCode;

    /**
     * HIS企业名称
     */
    @Schema(description = "HIS企业名称")
    @NotBlank(message = "HIS企业名称不能为空")
    @Size(max = 30, message = "HIS企业名称长度不能超过30个字符")
    @TableField("his_entp_name")
    private String hisEntpName;

    /**
     * 请求数据
     */
    @Schema(description = "请求数据")
    @Size(max = 100, message = "请求数据长度不能超过100个字符")
    @TableField("request_data")
    private String requestData;

    /**
     * 响应数据
     */
    @Schema(description = "响应数据")
    @Size(max = 100, message = "响应数据长度不能超过100个字符")
    @TableField("response_data")
    private String responseData;

    /**
     * 两定接口同步状态：0未同步，1已同步
     */
    @Schema(description = "两定接口同步状态：0未同步，1已同步")
    @Size(max = 100, message = "两定接口同步状态：0未同步，1已同步长度不能超过100个字符")
    @TableField("hsa_sync_status")
    private String hsaSyncStatus;

    /**
     * 两定接口同步状态时间
     */
    @Schema(description = "两定接口同步状态时间")
    @TableField("hsa_sync_time")
    private LocalDateTime hsaSyncTime;

    /**
     * 两定接口同步备注
     */
    @Schema(description = "两定接口同步备注")
    @Size(max = 100, message = "两定接口同步备注长度不能超过100个字符")
    @TableField("hsa_sync_remark")
    private String hsaSyncRemark;

    /**
     * HSA3513状态
     */
    @Schema(description = "HSA3513状态")
    @Size(max = 100, message = "HSA3513状态长度不能超过100个字符")
    @TableField("hsa3513_status")
    private String hsa3513Status;

    /**
     * 删除标志（0代表正常,1代表删除）
     */
    @Schema(description = "删除标志（0代表正常,1代表删除）")
    @Size(max = 100, message = "删除标志（0代表正常,1代表删除）长度不能超过100个字符")
    @TableField("delete_flag")
    private String deleteFlag;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    @Size(max = 100, message = "创建者长度不能超过100个字符")
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 创建日期
     */
    @Schema(description = "创建日期")
    @TableField("create_date")
    private LocalDate createDate;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    @Size(max = 100, message = "更新者长度不能超过100个字符")
    @TableField("update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @TableField("remark")
    private String remark;

    /**
     * 云速付用户ID
     */
    @Schema(description = "云速付用户ID")
    @TableField("ymf_user_id")
    private Long ymfUserId;

    /**
     * 云速付用户名
     */
    @Schema(description = "云速付用户名")
    @TableField("ymf_user_name")
    private String ymfUserName;

    /**
     * 云速付昵称
     */
    @Schema(description = "云速付昵称")
    @TableField("ymf_nick_name")
    private String ymfNickName;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    @TableField("sjh")
    private String sjh;

    /**
     * 处方序号
     */
    @Schema(description = "处方序号")
    @TableField("cfxh")
    private String cfxh;

    /**
     * 处方明细序号
     */
    @Schema(description = "处方明细序号")
    @TableField("cfmxxh")
    private String cfmxxh;

    /**
     * 患者ID
     */
    @Schema(description = "患者ID")
    @Size(max = 100, message = "患者ID长度不能超过100个字符")
    @TableField("patient_id")
    private String patientId;

    /**
     * 患者卡号
     */
    @Schema(description = "患者卡号")
    @Size(max = 100, message = "患者卡号长度不能超过100个字符")
    @TableField("pat_card_no")
    private String patCardNo;

    /**
     * 退货状态：0未退货，1已退货
     */
    @Schema(description = "退货状态：0未退货，1已退货")
    @Size(max = 100, message = "退货状态：0未退货，1已退货长度不能超过100个字符")
    @TableField("return_status")
    private String returnStatus;

    /**
     * 退货时间
     */
    @Schema(description = "退货时间")
    @TableField("return_time")
    private LocalDateTime returnTime;

    /**
     * 退货备注
     */
    @Schema(description = "退货备注")
    @Size(max = 100, message = "退货备注长度不能超过100个字符")
    @TableField("return_remark")
    private String returnRemark;

    /**
     * 退货溯源码信息
     */
    @Schema(description = "退货溯源码信息")
    @Size(max = 100, message = "退货溯源码信息长度不能超过100个字符")
    @TableField("return_drugtracinfo")
    private String returnDrugtracinfo;

    /**
     * 退货数量
     */
    @Schema(description = "退货数量")
    @TableField("min_unit_sel_retn_cnt")
    private BigDecimal minUnitSelRetnCnt;

    /**
     * 退货单号
     */
    @Schema(description = "退货单号")
    @NotBlank(message = "退货单号不能为空")
    @Size(max = 100, message = "退货单号长度不能超过100个字符")
    @TableField("his_unique_key")
    private String hisUniqueKey;

    /**
     * 库存数量
     */
    @Schema(description = "库存数量")
    @TableField("inv_cnt")
    private BigDecimal invCnt;

    /**
     * 包装换算系数
     */
    @Schema(description = "包装换算系数")
    @TableField("min_dose_count")
    private BigDecimal minDoseCount;

    /**
     * 药品材料标识 0:药品 1:材料
     */
    @Schema(description = "药品材料标识 0:药品 1:材料")
    @Size(max = 100, message = "药品材料标识 0:药品 1:材料长度不能超过100个字符")
    @TableField("material_or_drug")
    private String materialOrDrug;

    /**
     * 组合主键
     */
    @Schema(description = "组合主键")
    @NotBlank(message = "组合主键不能为空")
    @Size(max = 100, message = "组合主键长度不能超过100个字符")
    @TableField("composite_key")
    private String compositeKey;

    /**
     * 医院ID
     */
    @Schema(description = "医院ID")
    @Size(max = 100, message = "医院ID长度不能超过100个字符")
    @TableField("hospital_id")
    private String hospitalId;

    /**
     * 费用明细序列号
     */
    @Schema(description = "费用明细序列号")
    @Size(max = 100, message = "费用明细序列号长度不能超过100个字符")
    @TableField("feedetl_sn")
    private String feedetlSn;

    /**
     * 外部处方ID
     */
    @Schema(description = "外部处方ID")
    @Size(max = 100, message = "外部处方ID长度不能超过100个字符")
    @TableField("out_pres_id")
    private String outPresId;

    /**
     * 机构ID
     */
    @Schema(description = "机构ID")
    @Size(max = 100, message = "机构ID长度不能超过100个字符")
    @TableField("org_id")
    private String orgId;

    /**
     * 科室ID
     */
    @Schema(description = "科室ID")
    @Size(max = 10, message = "科室ID长度不能超过10个字符")
    @TableField("dept_id")
    private String deptId;

    /**
     * 发药单类型;1: 住院处方, 2:门诊处方
     */
    @Schema(description = "发药单类型;1: 住院处方, 2:门诊处方")
    @Size(max = 10, message = "门诊住院标志长度不能超过10个字符")
    @TableField("sd_dps")
    private String sdDps;
} 
