package com.zsm.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 发药单明细响应VO
 *
 * <AUTHOR>
 * @date 2025/06/03
 */
@Data
@Schema(description = "发药单明细响应VO")
public class DispensingRecordDetailVo {

    @Schema(description = "明细ID")
    private Long id;

    @Schema(description = "发药单ID (id_dps)")
    private String idDps;

    @Schema(description = "处方号 (cfxh)")
    private String cfxh;

    @Schema(description = "处方明细序号 (cfmxxh)")
    private String cfmxxh;

    @Schema(description = "药品编码 (drug_code)")
    private String drugCode;

    @Schema(description = "药品名称 (na_fee)")
    private String naFee;

    @Schema(description = "单价 (price_sale)")
    private BigDecimal priceSale;

    @Schema(description = "数量 (sel_retn_cnt)")
    private Integer selRetnCnt;

    @Schema(description = "金额 (amt_total)")
    private BigDecimal amtTotal;

    @Schema(description = "包装单位 (unit_sale)")
    private String unitSale;

    @Schema(description = "包装系数 (unit_sale_factor)")
    private Integer unitSaleFactor;

    @Schema(description = "追溯码 (drugtracinfo)")
    private String drugtracinfo;

    @Schema(description = "任务明细ID (ysf_sto_tc_task_sub.id_sub)")
    private Long idSub;

    @Schema(description = "是否已扫码 (ysf_sto_tc_task_sub.fg_scanned), 0=未扫码, 1=已扫码")
    private String fgScanned;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 已采集的追溯码数量
     */
    @Schema(description = "已采集的追溯码数量")
    private Integer tracCnt;
} 