# 医保费用明细出参示例

```json
{
          "bilg_dr_codg": "D341202017446",
          "bas_medn_flag": "0",
          "med_type": "21",
          "hilist_code": "001204000020100-ABBB0001",
          "memo": "",
          "opter_id": "E0C5E0911C4081C4",
          "etip_flag": "0",
          "tcmdrug_used_way": "1",
          "acord_dept_name": "妇科",
          "selfpay_prop": 0,
          "bilg_dr_name": "妇科",
          "lmt_used_flag": "0",
          "orders_dr_code": "",
          "det_item_fee_sumamt": 6.7,
          "hilist_name": "静脉采血",
          "mdtrt_id": "34122025060184573770",
          "dscg_tkdrug_flag": "0",
          "pric": 6.7,
          "feedetl_sn": "123782417",
          "inscp_scp_amt": 6.7,
          "bilg_dept_codg": "230",
          "overlmt_amt": 0,
          "list_type": "201",
          "preselfpay_amt": 0,
          "hosp_appr_flag": "1",
          "med_chrgitm_type": "05",
          "rx_drord_no": "578947284",
          "med_list_codg": "001204000020100-ABBB0001",
          "hi_nego_drug_flag": "0",
          "orders_dr_name": "",
          "medins_list_name": "静脉采血",
          "bilg_dept_name": "妇科",
          "etip_hosp_code": "",
          "pric_uplmt_amt": 7,
          "payLoc": "2",
          "opter_name": "太和县人民医院",
          "fee_ocur_time": 1749522121000,
          "opt_time": 1749522511000,
          "fulamt_ownpay_amt": 0,
          "cnt": 1,
          "matn_fee_flag": "0",
          "setl_id": "34122025061066477635",
          "drt_reim_flag": "0",
          "medins_list_codg": "001204000020100-ABBB0001",
          "acord_dept_codg": "230",
          "chrgitm_lv": "01"
        }
```

序号	参数代码	参数名称	参数类型	参数长度	代码标识	是否非空	说明
1	mdtrt_id	就诊ID	字符型	30		Y	
2	setl_id	结算ID	字符型	30		Y	
3	feedetl_sn	费用明细流水号	字符型	30		Y	
4	rx_drord_no	处方/医嘱号	字符型	30			
5	payLoc	支付地点类别	字符型	6	Y		字典（PAY_LOC）
6	med_type	医疗类别	字符型	6	Y	Y	
7	fee_ocur_time	费用发生时间	日期时间型			Y	yyyy-MM-dd HH:mm:ss
8	cnt	数量	数值型	16,4		Y	
9	pric	单价	数值型	16,6		Y	
10	sin_dos_dscr	单次剂量描述	字符型	200			
11	used_frqu_dscr	使用频次描述	字符型	200			
12	prd_days	周期天数	数值型	6,2			
13	medc_way_dscr	用药途径描述	字符型	200			
14	det_item_fee_sumamt	明细项目费用总额	数值型	16,2		Y	
15	pric_uplmt_amt	定价上限金额	数值型	16,6		Y	
16	selfpay_prop	自付比例	数值型	5,4			
17	fulamt_ownpay_amt	全自费金额	数值型	16,2			
18	overlmt_amt	超限价金额	数值型	16,2			
19	preselfpay_amt	先行自付金额	数值型	16,2			
20	inscp_scp_amt	符合政策范围金额	数值型	16,2			
21	chrgitm_lv	收费项目等级	字符型	3	Y	Y	
22	hilist_code	医保目录编码	字符型	50		Y	
23	hilist_name	医保目录名称	字符型	200		Y	
24	list_type	目录类别	字符型	6	Y	Y	
25	med_list_codg	医疗目录编码	字符型	50		Y	
26	medins_list_codg	医药机构目录编码	字符型	150		Y	
27	medins_list_name	医药机构目录名称	字符型	100		Y	
28	med_chrgitm_type	医疗收费项目类别	字符型	6	Y	Y	
29	prodname	商品名	字符型	2000			
30	spec	规格	字符型	200			
31	dosform_name	剂型名称	字符型	200		Y	
32	bilg_dept_codg	开单科室编码	字符型	30			
33	bilg_dept_name	开单科室名称	字符型	100			
34	bilg_dr_codg	开单医生编码	字符型	30			
35	bilg_dr_name	开单医师姓名	字符型	50			
36	acord_dept_codg	受单科室编码	字符型	30			
37	acord_dept_name	受单科室名称	字符型	100			
38	orders_dr_code	受单医生编码	字符型	30			
39	orders_dr_name	受单医生姓名	字符型	50			
40	lmt_used_flag	限制使用标志	字符型	3	Y		
41	hosp_prep_flag	医院制剂标志	字符型	3	Y		
42	hosp_appr_flag	医院审批标志	字符型	3	Y		
43	tcmdrug_used_way	中药使用方式	字符型	6	Y		
44	prodplac_type	生产地类别	字符型	6	Y		
45	bas_medn_flag	基本药物标志	字符型	3	Y		
46	hi_nego_drug_flag	医保谈判药品标志	字符型	3	Y		
47	chld_medc_flag	儿童用药标志	字符型	3	Y		
48	etip_flag	外检标志	字符型	3	Y		
49	etip_hosp_code	外检医院编码	字符型	30			
50	dscg_tkdrug_flag	出院带药标志	字符型	3	Y		
51	list_sp_item_flag	目录特项标志	字符型	3	Y		特检特治项目或特殊药品
52	matn_fee_flag	生育费用标志	字符型	6	Y		
53	drt_reim_flag	直报标志	字符型	3	Y		
54	memo	备注	字符型	500			
55	opter_id	经办人ID	字符型	20			
56	opter_name	经办人姓名	字符型	50			
57	opt_time	经办时间	日期时间型				yyyy-MM-dd HH:mm:ss
58	chrg_bchno	收费批次号	字符型	30			
