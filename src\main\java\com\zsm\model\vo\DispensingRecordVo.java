package com.zsm.model.vo;

import com.zsm.entity.YsfStoDps;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 发药单列表响应VO
 *
 * <AUTHOR>
 * @date 2025/06/03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "发药单列表响应VO")
public class DispensingRecordVo extends YsfStoDps {

    // ================= 基础字段信息 =================
    
    @Schema(description = "发药记录id")
    private Long recordId;

    @Schema(description = "发药明细id")
    private Long recordDetailId;

    @Schema(description = "原发药明细id")
    private String oriDetailId;

    @Schema(description = "费用明细id")
    private String idFee;

    @Schema(description = "费用名称")
    private String naFee;

    @Schema(description = "医嘱类别")
    private String sdClassify;

    @Schema(description = "发药单标记")
    private String fgDps;

    @Schema(description = "发药标志")
    private String sendFlag;

    @Schema(description = "发药时间")
    private LocalDateTime sendTime;

    @Schema(description = "零售单据号")
    private String rtalDocno;

    @Schema(description = "销售出库单据号")
    private String stooutNo;

    @Schema(description = "病区id")
    private String patWardId;

    @Schema(description = "病区名称")
    private String patWardName;

    @Schema(description = "发药科室id")
    private String fyyf;

    @Schema(description = "发药科室id")
    private String deptId;

    @Schema(description = "药师证件号码")
    private String pharCertno;

    @Schema(description = "药师姓名")
    private String pharName;

    @Schema(description = "药师执业资格证号")
    private String pharPracCertNo;

    @Schema(description = "销售/退货时间")
    private LocalDateTime selRetnTime;

    // ================= 药品信息 =================

    @Schema(description = "HIS系统中的药品唯一编码")
    private String hisDrugCode;

    @Schema(description = "医疗目录编码")
    private String medListCodg;

    @Schema(description = "规格")
    private String spec;

    @Schema(description = "生产企业名称")
    private String prodentpName;

    @Schema(description = "定点医药机构目录编号")
    private String fixmedinsHilistId;

    @Schema(description = "定点医药机构目录名称")
    private String fixmedinsHilistName;

    @Schema(description = "生产批号")
    private String manuLotnum;

    @Schema(description = "生产日期")
    private LocalDateTime manuDate;

    @Schema(description = "有效期止")
    private LocalDateTime expyEnd;

    @Schema(description = "批次号")
    private String bchno;

    @Schema(description = "处方药标志")
    private String rxFlag;

    @Schema(description = "拆零标志")
    private String trdnFlag;

    @Schema(description = "his的剂量单位")
    private String hisDosUnit;

    @Schema(description = "his的包装单位")
    private String hisPacUnit;

    @Schema(description = "转换比")
    private Integer hisConRatio;

    // ================= 患者及本次发放明细信息 =================

    @Schema(description = "定点医药机构批次流水号")
    private String fixmedinsBchno;

    @Schema(description = "住院id")
    private String patInHosId;

    @Schema(description = "就医流水号")
    private String mdtrtSn;

    @Schema(description = "人员姓名")
    private String psnName;

    @Schema(description = "床位号")
    private String bedNo;

    @Schema(description = "就诊结算类型")
    private String mdtrtSetlType;

    @Schema(description = "医嘱id")
    private String orderId;

    @Schema(description = "开方医师证件号码")
    private String prscDrCertno;

    @Schema(description = "开方医师姓名")
    private String prscDrName;

    @Schema(description = "销售/退货数量")
    private Integer selRetnCnt;

    @Schema(description = "销售/退货单位")
    private String selRetnUnit;

    @Schema(description = "医院医嘱主表唯一值")
    private String cfxh;

    @Schema(description = "医院医嘱明细表唯一值")
    private String cfmxxh;

    // ================= 扩展字段 =================

    @Schema(description = "发药单状态 (fg_status from ysf_sto_dps)")
    private String dispensingFgStatus;

    @Schema(description = "关联的最新任务状态 (fg_status from ysf_sto_tc_task)")
    private String taskStatus;

    @Schema(description = "关联的最新任务ID (id_task from ysf_sto_tc_task)")
    private Long taskId;

} 