package com.zsm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 扫码任务记录表实体类
 * 
 * <AUTHOR>
 * @since 2025-06-02
 */
@Data
@TableName("ysf_sto_tc_task")
@Schema(description = "扫码任务记录表")
public class YsfStoTcTask implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 任务主键
     */
    @Schema(description = "任务主键")
    @TableId(value = "id_task", type = IdType.AUTO)
    private Long idTask;

    /**
     * 任务类型;1.主动任务,2.被动任务
     */
    @Schema(description = "任务类型;1.主动任务,2.被动任务")
    @Size(max = 100, message = "任务类型;1.主动任务,2.被动任务长度不能超过100个字符")
    @TableField("sd_task_type")
    private String sdTaskType;

    /**
     * 业务类型
     */
    @Schema(description = "业务类型")
    @Size(max = 100, message = "业务类型长度不能超过100个字符")
    @TableField("sd_tc_status")
    private String sdTcStatus;

    /**
     * 关联的业务单号: 入库单号/发药单号/退药单号等
     */
    @Schema(description = "关联的业务单号: 入库单号/发药单号/退药单号等")
    @Size(max = 100, message = "关联的业务单号: 入库单号/发药单号/退药单号等长度不能超过100个字符")
    @TableField("cd_biz")
    private String cdBiz;

    /**
     * 关联科室/部门: 来源于ysf_sto_dept.id_dept
     */
    @Schema(description = "关联科室/部门: 来源于ysf_sto_dept.id_dept")
    @Size(max = 100, message = "关联科室/部门: 来源于ysf_sto_dept.id_dept长度不能超过100个字符")
    @TableField("id_dept")
    private String idDept;

    /**
     * 操作员ID: 被动任务时绑定具体操作员
     */
    @Schema(description = "操作员ID: 被动任务时绑定具体操作员")
    @Size(max = 100, message = "操作员ID: 被动任务时绑定具体操作员长度不能超过100个字符")
    @TableField("id_user")
    private String idUser;

    /**
     * 任务过期时间: 如发药时间和当前时间的差额超过30分钟，那会自动将这个任务推送到医保接口。如果始终没有关联到发药时间，那么6个小时后会自动失效
     */
    @Schema(description = "任务过期时间: 如发药时间和当前时间的差额超过30分钟，那会自动将这个任务推送到医保接口。如果始终没有关联到发药时间，那么6个小时后会自动失效")
    @TableField("dt_expire")
    private LocalDate dtExpire;

    /**
     * 任务状态: 0.待处理, 1.已完成, 2.已失效
     */
    @Schema(description = "任务状态: 0.待处理, 1.已完成, 2.已失效")
    @Size(max = 100, message = "任务状态: 0.待处理, 1.已完成, 2.已失效长度不能超过100个字符")
    @TableField("fg_status")
    private String fgStatus;

    /**
     * 优先级: 1.普通, 2.紧急
     */
    @Schema(description = "优先级: 1.普通, 2.紧急")
    @Size(max = 100, message = "优先级: 1.普通, 2.紧急长度不能超过100个字符")
    @TableField("fg_priority")
    private String fgPriority;

    /**
     * 任务备注
     */
    @Schema(description = "任务备注")
    @Size(max = 100, message = "任务备注长度不能超过100个字符")
    @TableField("memo")
    private String memo;

    /**
     * 机构编号
     */
    @Schema(description = "机构编号")
    @Size(max = 100, message = "机构编号长度不能超过100个字符")
    @TableField("id_org")
    private String idOrg;

    /**
     * 医疗机构编码
     */
    @Schema(description = "医疗机构编码")
    @Size(max = 100, message = "医疗机构编码长度不能超过100个字符")
    @TableField("org_id")
    private String orgId;

    /**
     * 医疗机构名称
     */
    @Schema(description = "医疗机构名称")
    @NotBlank(message = "医疗机构名称不能为空")
    @Size(max = 30, message = "医疗机构名称长度不能超过30个字符")
    @TableField("org_name")
    private String orgName;

    /**
     * 乐观锁
     */
    @Schema(description = "乐观锁")
    @Size(max = 100, message = "乐观锁长度不能超过100个字符")
    @TableField("revision")
    private String revision;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    @Size(max = 100, message = "创建者长度不能超过100个字符")
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    @Size(max = 100, message = "更新者长度不能超过100个字符")
    @TableField("update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @TableField("remark")
    private String remark;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @Schema(description = "删除标志（0代表存在 1代表删除）")
    @Size(max = 100, message = "删除标志（0代表存在 1代表删除）长度不能超过100个字符")
    @TableField("del_flag")
    private String delFlag;
} 
