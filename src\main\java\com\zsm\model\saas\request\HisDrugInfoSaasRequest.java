package com.zsm.model.saas.request;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.util.List;

/**
 * 同步his药品信息saas入参
 *
 * <AUTHOR>
 * @date 2025/06/14
 */
@Data
public class HisDrugInfoSaasRequest {

    @NotBlank(message = "医疗机构代码不能为空【medicalCode】")
    private String medicalCode;

    @NotBlank(message = "医疗机构名称")
    private String medicalName;

    @NotBlank(message = "requestID不能为空")
    private String requestID;

    private List<HisDrugInfoSaasApiData> dataList;

}
